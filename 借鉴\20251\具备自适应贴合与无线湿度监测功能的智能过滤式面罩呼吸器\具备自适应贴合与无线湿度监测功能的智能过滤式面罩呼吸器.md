Smart filtering facepiece respirator with self-adaptive fit and wireless humidity monitoring

具备自适应贴合与无线湿度监测功能的智能过滤式面罩呼吸器

## 摘要

空气传播疾病的广泛流行改变了我们的生活方式，呼吸器已成为日常生活必需品。然而，由于人类面部尺寸和形状的多样性，寻找贴合良好的呼吸器具有挑战性，这可能会影响防护效果。此外，现有呼吸器在长期连续使用时无法告知用户空气质量状况。本文介绍了一种集成湿度传感器和压力感应反馈的智能过滤式面罩呼吸器，可实现自适应贴合调节并保持适当密合度。该湿度检测传感器采用激光诱导石墨烯技术，基于介电弹性海绵的压力传感器阵列可监测呼吸器与用户面部的接触情况，提供实时闭环反馈和佩戴者贴合状态。这些薄膜传感器表现出卓越性能，如0.131%的低湿度滞后性和${0.23} \pm  {0.02}$ kPa的精确压力检测限。通过自适应调节模式，整体贴合系数较商用呼吸器平均提升${10}\%$。这种贴合系数的显著改善与创新设计相结合，有望推动下一代面罩呼吸器作为重要个人防护装备的发展。

## 引言

呼吸器作为降低空气传播病原体感染风险的重要个人防护装备$\left\lbrack  {1,2}\right\rbrack$，已成为全球日常生活中不可或缺的组成部分[3]。尽管应用广泛，但多数过滤式面罩呼吸器与弹性半面罩呼吸器长期使用时缺乏环境监测功能，且存在佩戴舒适性问题[4]。传统呼吸器最突出的问题在于商业产品无法适配用户多样化的面部尺寸与形状，导致长期佩戴时密合度与舒适性欠佳[5,6]。在美国，使用者虽需通过初始适配测试，但由于面部活动、湿气及面部特征变化等因素，维持数小时的有效密封仍具挑战性，进而影响防护效果[5-7]。不合规的呼吸器常导致口鼻处出现缝隙，降低有害颗粒过滤效率[8]。此外，个体面部特征差异显著(如脸型大小不一)，而呼吸器往往仅提供单一尺寸[9]。面罩内部湿气积聚及眼镜起雾问题也普遍存在，严重影响使用体验，这进一步凸显了对更具适应性、个性化呼吸器的需求。现有呼吸器解决方案的另一局限是缺乏实时环境监测能力，传统产品无法提供周围空气质量关键参数(如湿度)，而该指标对确保最佳防护性能与佩戴舒适性至关重要。研究表明当面罩内相对湿度超过${60}\%$时，使用者会产生热不适感，可能引发热应激反应并阻碍正常呼吸[10]。因此亟需改进现有呼吸器，在保持良好密合度的同时实现高精度面罩内湿度监测。

---

* 通讯作者。可穿戴智能系统与健康中心(WISH Center)，物质与系统研究所，佐治亚理工学院，亚特兰大，GA 30332，美国。

** 通讯作者。应用纳米与热科学实验室，机械工程系，首尔国立大学，冠岳路1号，冠岳区，首尔08826，韩国。

** 通讯作者。机械·机器人·能源工程系，东国大学，必洞路1街30号，中区，首尔04620，韩国。

电子邮箱:<EMAIL>(S.-H. Ko)，<EMAIL>(J. Lee)，<EMAIL>(W.-H. Yeo)。

${}^{1}$ K. Kwon与Y. J. Lee对本研究贡献均等。

---

当前研究与产品开发已着手解决商用呼吸器的局限性。值得注意的是，近期研究尝试将传感器与电子元件集成至呼吸面罩，以监测呼吸活动[3,11-16]、面罩密合度[8,17,18]及其他生命体征[19-21]。然而这些技术改进常涉及复杂笨重的电子组件，反而加重佩戴不适感。此外，现有方案缺乏实时反馈机制，用户无法获知面罩密合状态或呼吸空气质量即时数据。鉴于传统呼吸器的设计局限，将无线实时可穿戴技术融入呼吸器设计的需求日益增长。与此同时，配备二氧化碳[22-24]与湿度$\left\lbrack  {{16},{25} - {30}}\right\rbrack$等环境传感器的智能呼吸器研发也备受关注。尽管软材料应用与生理指标无线监测已取得显著进展，但大量传感器仍采用刚性设计，未能实现基于传感数据的无线可穿戴监测及面罩自适应调节功能。由此可见，虽然呼吸器技术发展迅猛，但缺乏自适应设计与环境监测能力仍是现有方案的关键缺陷。

本文介绍了一种智能过滤式面罩呼吸器(SFFR)系统，该系统集成石墨烯传感器实现实时湿度监测，并采用压力传感器与执行器组成的独特自适应贴合机制，以提升佩戴舒适性并确保最佳呼吸防护性能。石墨烯传感器在湿度测量中兼具高灵敏度与选择性，同时保持轻质柔性特性，可连续监测使用者呼吸环境以评估面罩性能及整体空气质量。该自适应贴合机制通过八个压力电极检测用户面部轮廓，配合两个电机调节面罩位置，确保面部密封既牢固又舒适，有效解决面罩移位、眼镜起雾和皮肤刺激等常见问题。本研究提出的SFFR设计提供了更全面的解决方案，有望显著改善个人防护装备的整体佩戴体验。表1通过与现有呼吸器研究的对比凸显本工作的创新性，其中阴影区域展示了兼具舒适性提升与空气质量监测功能的理想SFFR特性。表S1对传感器性能进行对比分析，重点比较本研究与既往呼吸器在检测限、响应/恢复时间等指标上的差异。数据显示SFFR在保持相当传感性能的同时，还为个人呼吸防护提供了多功能解决方案。我们相信这项研究的面罩呼吸器系统将成为满足用户驱动型高效防护呼吸器所有技术参数的独家解决方案。

## 2. 材料与方法

### 2.1. 压力传感器制备与表征

SFFR原型机的电容式压力传感器制备包含两个主要工序:1)传感器阵列电极制备(含电极与介电层生产)；2)电路制作与传感器封装(图S1)。电极层制备采用聚酰亚胺(PI)和铜(Cu)层的激光微加工技术，互连部件双面用聚酰亚胺薄膜封装，传感器电极部件仅单面封装。压力传感器阵列封装时，将两个感应电极与作为介电层的Ecoflex海绵夹层折叠组合，再通过弹性体(Eco-flex 00-30, Smooth-On)和硅胶(Silbione A-4717, Factor II Inc.)与呼吸器结合。采用柔性印刷电路板(fPCB)制作电路并进行传感器封装，所有电子元件通过回流焊工艺安装。为提高电路机械柔性，采用激光切割去除非必要区域。电源管理系统包含配备滑动开关和圆形磁吸充电口的锂聚合物电池组，集成电路下方放置低模量弹性体(Ecoflex Gel, Smooth-On)以减小应变。完整电子系统采用额外弹性体(Ecoflex 00-30, Smooth-On)进行软包封装，仅暴露开关与充电接口。实验装置包括测量机械性能的数字测力计(M5-5, Mark-10)和电动测试台(ESM303, Mark-10)，以及测量电容的LCR表(Model 891, BK Precision)。疲劳测试时测力计平头以${20}\mathrm{\;{mm}}/\mathrm{{min}}$速度垂直运动5000次循环。

表1

近期开发的呼吸器性能对比

<table><tbody><tr><td rowspan="2">参考文献</td><td rowspan="2">集成一体化系统</td><td colspan="2">自适应贴合系统</td><td colspan="3">传感器反馈</td><td colspan="2">气体传感能力</td></tr><tr><td>实时自动适配</td><td>云存储</td><td>传感器类型</td><td>材料</td><td>无线</td><td>材料</td><td>无线</td></tr><tr><td>本研究</td><td>是</td><td>是</td><td>是</td><td>压力</td><td>铜/糖+Ecoflex复合材料</td><td>是</td><td>激光诱导石墨烯(LIG)</td><td>是</td></tr><tr><td>[22]</td><td>-</td><td>-</td><td>-</td><td>-</td><td>-</td><td>-</td><td>${\mathrm{{La}}}_{2}{\mathrm{O}}_{2}\mathrm{\;S}$</td><td>是</td></tr><tr><td>[15]</td><td>-</td><td>-</td><td>是</td><td>压力</td><td>金/特氟龙AF</td><td>是</td><td>-</td><td>-</td></tr><tr><td>[47]</td><td>-</td><td>-</td><td>-</td><td>-</td><td>-</td><td>-</td><td>钨掺杂碳纳米管(WCNT)</td><td>-</td></tr><tr><td>[48]</td><td>-</td><td>-</td><td>-</td><td>-</td><td>-</td><td>-</td><td>$\mathrm{{RGO}}/{\mathrm{{SnO}}}_{2}$</td><td>是</td></tr><tr><td>[49]</td><td>-</td><td>-</td><td>-</td><td>温度</td><td>商用</td><td>-</td><td>沸石咪唑酯骨架-8/聚丙烯腈(ZIF-8/PAN)</td><td>是</td></tr><tr><td>[25]</td><td>-</td><td>-</td><td>-</td><td>-</td><td>-</td><td>-</td><td>多孔硅</td><td>是</td></tr><tr><td>[17]</td><td>-</td><td>是</td><td>-</td><td>-</td><td>-</td><td>-</td><td>-</td><td>-</td></tr><tr><td>[11]</td><td>-</td><td>-</td><td>-</td><td>压力</td><td>碳纳米管/聚二甲基硅氧烷(CNT/PDMS)</td><td>是</td><td>-</td><td>-</td></tr><tr><td>[50]</td><td>-</td><td>-</td><td>-</td><td>压力</td><td>微针阵列(MTP)</td><td>是</td><td>-</td><td>-</td></tr><tr><td>[18]</td><td rowspan="2">-</td><td>-</td><td>是</td><td>应变</td><td>$\mathrm{{Ag}}/\mathrm{{PI}}$</td><td>是</td><td>-</td><td>-</td></tr><tr><td></td><td></td><td></td><td>温度</td><td>$\mathrm{{Ag}}/\mathrm{{PI}}$</td><td>是</td><td></td><td></td></tr><tr><td>[21]</td><td>-</td><td>-</td><td>-</td><td>超声波</td><td>商用</td><td>是</td><td>-</td><td>-</td></tr><tr><td></td><td></td><td></td><td></td><td>温度</td><td>商用</td><td>是</td><td></td><td></td></tr><tr><td>[16]</td><td>-</td><td>-</td><td>-</td><td>-</td><td>-</td><td>-</td><td>聚丙烯/硅(PP/Si)</td><td>是</td></tr><tr><td>[12]</td><td>-</td><td>-</td><td>-</td><td>呼吸</td><td>可拉伸摩擦纳米发电机(FEP/铝)</td><td>-</td><td>-</td><td>-</td></tr><tr><td>[19]</td><td>-</td><td>-</td><td>-</td><td>谐波</td><td>银纳米线(AgNW)</td><td>是</td><td>-</td><td>-</td></tr><tr><td>[14]</td><td>-</td><td>-</td><td>-</td><td>应变</td><td>石墨烯纳米片/聚己内酯(GNP/PCL)</td><td>是</td><td>-</td><td>-</td></tr></tbody></table>

### 2.2 激光诱导石墨烯传感器

我们采用${532}\mathrm{\;{nm}}\mathrm{\;{Nd}}$:YAG连续波激光器(Lighthouse Photonics公司)在PI薄膜(CS Hyde公司18-2F型号)上生成石墨烯。激光束穿过包含振镜扫描仪和远心透镜振镜扫描系统的光学系统。通过集成振镜扫描仪的计算机辅助图案化软件，实现对激光功率、扫描速度及预设CAD模型图案的精确控制。该系统以$- {150}\mathrm{{mW}}$功率、${150}\mathrm{\;{mm}}/\mathrm{s}$扫描速率辐照PI薄膜，将其转化为激光诱导石墨烯(LIG)。电路制造与传感器集成流程与压力传感器生产工艺相同。采用场发射扫描电镜(FE-SEM，ZEISS SUORA 55VP)和光学显微镜(OLYMPUS BX51)分析表面及截面形貌。通过拉曼光谱仪(Renishaw inVia)研究分子指纹特征，探究光与分子相互作用。焦耳热实验中，使用直流电源(KEITHLEY 2231A-30-3)和红外热像仪(FLIR A645)，以$2\mathrm{\;V} - {10}\mathrm{\;V}$电压、$2\mathrm{\;V}$间隔施加电压，测量传感器电极背面LIG加热器的温度。

### 2.3 湿度传感测试

采用定制气室调控特定体积气体流量进行湿度测试。连接湿空气与干燥空气气源的质量流量控制器(MFCs)精确控制蒸汽流量，湿空气混入氧气，干燥空气混入氮气并置于储水罐中。为验证LIG传感器湿度检测的重现性，每次调节湿度前均使用气室(Phocos MPS2000气体传感器测量仪)将蒸汽阀密封并调至0%湿度，该设备可在5%-95%相对湿度范围内自动调控，误差不超过0.5%。实验探究20$\%$至${100}\%$的湿度范围，以${20}\%$为增量逐步提升。通过源表(Keithley 2400型号)连接LIG传感器，运行定制软件实时监测传感器电阻变化。

### 2.4 数据采集系统的固件与层级结构

SFFR系统固件设计采用多层功能架构，实现湿度传感器监测、压力传感器阵列及自适应贴合功能的协同运作。固件后端功能层包括:原始传感器数据处理层、设备功能控制执行与反馈控制层、以及功耗优化电源管理层。以低功耗SoC微控制器(Nordic Semiconductor NRF52832)为主处理器，开发无线BLE(蓝牙低功耗)协议及多通信层以简化通信任务。传感器数据处理层负责处理嵌入式传感器的原始数据，控制层通过计算传感器数据与接收指令触发响应，执行各类功能控制。

### 2.5 无线实时SFFR系统运行与自适应贴合

在无线实时SFFR系统中演示压力传感，需集成FDC1004传感器IC组件，通过双线接口(TWI)协议与微控制器(MCU)连接。该传感器测量电容变化并将其转换为数字信号，这些信号可通过设备固件解析利用。对于LIG传感，采用串行外设接口(SPI)连接的ADS1292低功耗模拟前端(AFE)传感器及惠斯通电桥电路，使MCU能精确测量电阻值。此外，通过编程电机驱动器(PDRV8210DRLR)经由MCU通用输入输出口(GPIO)发送数字信号控制电机运行。校准与自适应传感技术用于优化传感器检测灵敏度，从而提升系统整体精度。启动安卓应用时，设备会利用FDC1004固件协议自动校准呼吸器基准电容与电容增益。电容增益校准还通过一次性可编程(OTP)存储器更新增益系数，实现标准化电容测量。开发的用户友好型安卓应用具备实时压力检测、无线数据传输记录、带警报阵列的压力图表可视化、自适应电容传感与适配警报等核心功能。该应用通过固件用户界面(UI)层实现交互式压力数据监测与实时警报，电源管理层优化能耗，配合NRF52832 MCU的强大处理能力与能效提供无缝体验。采用红黄绿三色编码的适配警报系统直观显示密封效果:红色需调整，黄色可接受但需改进，绿色表示良好，深灰为过紧。该系统通过实时反馈辅助用户调整N95(R)呼吸器佩戴状态。基于多组人体实验数据，该系统支持实时压力监测与特征识别。在自动适配模式下，使用PortaCount®(PortaCount® Pro + Respirator Fit Tester 8038)测量了市售NIOSH认证N95过滤式面罩呼吸器(下称商用N95呼吸器)与本SFFR原型的适配系数。"NIOSH Approved"与"N95"是美国卫生与公众服务部在美国及多个司法管辖区注册的认证标志。实验遵循呼吸器测试协议(OSHA 29CFR1910.134)，测量四种不同情境下的适配系数。

### 2.6. 数据云计算接口与软件应用

将压力适配测试结果与湿度监测集成至远程云服务，显著提升了SFFR系统的实用性与功能性。压力与湿度传感器数据存储于云端服务器，便于远程访问与分析。这不仅保障了数据安全与可获取性(即使本地移动设备损坏或丢失)，还支持用户跨设备访问数据。该功能使用户能便捷追踪呼吸器使用时长、监测适配测试结果变化及湿度水平。通过云服务集成实现数据实时远程采集存储，支持大规模数据分析、统计机器学习应用，并能基于历史数据提供个性化反馈。相关专业人员可远程访问分析数据，优化监测与干预策略。匿名云端数据有助于洞察宏观趋势，推动SFFR整体设计与功能改进。

### 2.7. 人体受试者研究

五名18至40岁健康受试者在首尔国立大学批准的IRB协议(#2309/004-014)下参与研究。所有受试者在参与前均签署知情同意书并了解研究流程。

## 3. 结果与讨论

### 3.1. SFFR系统概述

图1A展示了描述SFFR系统各组件及传达本研究核心理念的示意图。如图所示，这款智能面罩呼吸器的基本框架由压力传感器阵列、基于激光诱导石墨烯(LIG)的湿度传感器、柔性电路和贴合度调节执行器构成，所有组件均集成在商用N95呼吸器上。采用薄膜式传感器对实现与皮肤无刺激的紧密贴合至关重要，可避免过度运动伪影[31-33]。基于聚酰亚胺(PI)基板的柔性电路负责采集智能呼吸器内部的实时面部贴合度与湿度数据，并通过无线方式将数据传至智能手机等便携设备。当检测到面罩佩戴不当时，智能手机依据压力传感器阵列数据自主启动执行器，通过蓝牙无线通信调节呼吸器贴合度。除自适应调节功能外，智能手机还会同步更新湿度水平——通过测量电阻比值确定呼吸器内部实时湿度质量，并向用户提示。所有采集的湿度与压力传感数据最终存储于云端供后续查阅，个人数据仅限用户或授权管理人员访问。图1B流程图描述了各模块构建感官反馈及SFFR系统运行的逻辑:LIG传感器监测湿度水平，压力传感器电极追踪用户面部八个接触点的贴合度，柔性电路汇总数据并无线传输至便携设备进行实时处理，处理后数据既存储于云端又反馈至调节执行器模块。当呼吸器贴合不良时，伺服电机通过头带向SFFR传递作用力，实现自主贴合调节。

### 3.2. 压力传感器的制备与表征

图2A展示了SFFR(智能柔性呼吸器)及其构成部件的图示，包括定制化磁吸充电电池、柔性压力传感器电路以及连接压力传感器阵列的柔性电极。该柔性电路内置微芯片，可实现实时压力数据采集并通过无线方式传输至便携设备(尺寸:5.5英寸$\times  5$×$\times  {2.5}$英寸，重量:1.5磅)。如图2B所示，压力传感器由两层PI/Cu电极夹着介电弹性海绵构成，其工作原理是通过测量Ecoflex海绵在轴向弹性变形时受压引起的电容增量。我们通过先在$\mathrm{{PI}}$薄膜上沉积$\mathrm{{Cu}}$材料，再按设计图案激光切割制成$\mathrm{{PI}}/\mathrm{{Cu}}$电极。Ecoflex海绵的制备采用1:1比例的糖与预固化Ecoflex混合，糖晶体在真空固化过程中保持不溶解状态。固化后将弹性体-糖复合材料浸入热水浴溶解糖分，最终形成可压缩的海绵状弹性泡沫。该泡沫经切割成型后作为介电材料置于$\mathrm{{PI}}/\mathrm{{Cu}}$电极间用于电容测量。尽管电阻式传感器具备多项优势，我们仍为呼吸器选择电容式压力传感器阵列设计，因其通常具有更高灵敏度与分辨率[34-36]。图2C呈现了构成传感器阵列的PI/Cu电极与Ecoflex海绵实物图:硅胶海绵置于$\mathrm{{PI}}/\mathrm{{Cu}}$电极上方，对应电极折叠包裹形成三明治结构。传感器阵列还设计有连接焊盘以便与排线对接(图S2)。顶层的Silbione和Ecoflex封装层能增强SFFR与皮肤表面的贴合度。为确定Ecoflex海绵最佳厚度并验证传感性能，我们测量了不同厚度($({1.0}\mathrm{\;{mm}},{1.5}\mathrm{\;{mm}},{2.5}\mathrm{\;{mm}}$至3.0$\mathrm{{mm}}$)传感器在变压力下的电容变化(图2D)。厚度对比表明1.0$\mathrm{{mm}}$海绵在给定压力范围内电容响应最优(实验装置见图S3)，这与材料力学模量均匀时薄样本更易变形的理论预期一致。因此选用1.0$\mathrm{{mm}}$厚度海绵制作传感器并进行表征。图2E显示该传感器在5000次压力循环中归一化电容保持高度稳定，证明其监测呼吸器佩戴状态的可靠性。该传感器检测限达${0.23} \pm  {0.02}\mathrm{{kPa}}$(图S4)，测量范围最高至${120}\mathrm{{kPa}}$，响应与恢复时间分别为${0.7} \pm  {0.1}$秒和${0.55} \pm  {0.05}$秒(图S5)。

### 3.3. 湿度传感器的制备与表征

图3A阐述了通过激光照射聚酰亚胺(PI)基底制备石墨烯传感器的简易方法。我们选择石墨烯作为传感材料是基于其卓越的电学特性[37]，而激光诱导石墨烯(LIG)电极的多孔结构通过提供更大的气体分子物理吸附表面积，增强了气敏性能$\left\lbrack  {{37},{38}}\right\rbrack$。本研究的石墨烯传感器还在另一侧集成了加热组件，因为高温能促进气态分子从石墨烯表面解吸$\left\lbrack  {{39},{40}}\right\rbrack$。如图所示，当激光在PI基底上刻写完传感器后，需在另一侧进行激光照射以制备石墨烯加热器。图3B分别展示了湿度传感器顶部和底部对应的传感器与加热器实物照片。虽然采用相同激光参数制备，但二者设计存在差异:湿度传感器的两个接触电极通过宽度仅${200\mu }\mathrm{m}$的细长电极相连(如光学显微镜图像所示)，这种设计既能实现湿度灵敏检测，又可捕捉微小湿度变化；而加热器采用矩形块状设计以实现PI薄膜的均匀加热，在SFFR闲置时加速水分子解吸。图3B的扫描电镜(SEM)图像显示LIG具有高度多孔结构，这种结构通过提供大量水分子附着位点显著提升了湿度传感器灵敏度。图3C的拉曼光谱为判断PI基底激光照射过程中石墨烯生成提供了关键依据:如图标注所示，石墨烯拉曼光谱包含D峰(约${1340}{\mathrm{\;{cm}}}^{-1}$)、$\mathrm{G}$峰(约${1585}{\mathrm{\;{cm}}}^{-1}$)和$2\mathrm{D}$峰(约${2696}{\mathrm{\;{cm}}}^{-1}$)等特征峰。通常D峰代表${\mathrm{{sp}}}^{2}$杂化$\mathrm{C}$键或缺陷存在，G峰对应一阶声子，2D峰反映二阶边界声子[41]。本研究中${\mathrm{I}}_{2\mathrm{D}}/{\mathrm{I}}_{\mathrm{G}}$比值为0.620，远低于2的单层石墨烯判定阈值；且相较于理想石墨烯2D峰位(2685${\mathrm{{cm}}}^{-1}$)，本研究$2\mathrm{D}$峰出现约11${\mathrm{{cm}}}^{-1}$的蓝移，这归因于激光加工过程中快速加热冷却导致的压缩应变[42,43]。

![01965753-5726-75f8-b5cf-d179c2f82f55\_4\_81\_146\_1582\_1767\_0.jpg](images/01965753-5726-75f8-b5cf-d179c2f82f55_4_81_146_1582_1767_0.jpg)

图1. SFFR系统及其运行机制概览。A) SFFR系统示意图，该系统通过压力传感器阵列和反馈控制实现呼吸器内部湿度监测及自适应密合调节。嵌入式无线电路将采集数据传输至便携设备，数据经处理后既用于实时反馈，又存储于云端供后续调阅。B) 流程图描述SFFR系统从信号感知到数据传输、处理及自调节密合的全过程。

![01965753-5726-75f8-b5cf-d179c2f82f55\_5\_95\_147\_1554\_941\_0.jpg](images/01965753-5726-75f8-b5cf-d179c2f82f55_5_95_147_1554_941_0.jpg)

图2. 压力传感器的制备与表征。A) SFFR系统分解图，包含柔性无线电路、磁吸充电电池及柔性压力传感器阵列。B) 单个压力传感器的制备流程。C) 压力传感器阵列实物图及SFFR系统内部结构。D) 不同弹性海绵厚度下施加压力对应的电容变化测量结果。E) 压力传感器在循环机械载荷下的归一化电容测试数据。

为评估基于激光诱导石墨烯(LIG)的湿度传感器性能，我们测量了传感器在湿度梯度上升时的归一化电阻变化。图3D展示了水分浓度阶梯式增加时对应的归一化电阻。对于石墨烯湿度传感器而言，水分子作为电子给体，其向石墨烯的电子转移会增大石墨烯的带隙，导致湿度升高时电阻增加[44]。因此，当湿度以$0\%$至${80}\%$的百分比区间(步长${20}\%$)递增时，LIG传感器的归一化电阻随湿度梯度同步上升。得益于石墨烯电极的高比表面积和优异电学特性，该传感器在${20}\%$相对湿度下仅呈现${0.131}\%$的超低滞后效应，并具有${19.29}\mathrm{\;s}$和${22.53}\mathrm{\;s}$的快速响应与恢复时间。鉴于N95口罩建议在高风险环境中每日佩戴，我们通过长期监测${40}\%$相对湿度下的归一化电阻变化来评估传感器稳定性(图S6)。结果表明，暴露于潮湿环境初期出现陡峭峰值后，归一化电阻逐渐趋于稳定。虽然电阻对湿度上升响应迅速，但随时间推移呈现渐进式上升趋势，这种轻微增幅可能源于水分子吸附/解吸速率差异，其中吸附过程更为迅速。除湿度传感性能测试外，我们还表征了LIG加热器性能(图3E)，其工作电压采用离散式递增。热成像图显示石墨烯加热器温度与施加电压呈线性关系，该关系通过回归方程$y = {21.189x} - {13.465}$(拟合优度${R}^{2} =$0.9789)量化。加热器在$6\mathrm{\;V}$或${163}\mathrm{\;{mW}}$电压下可达约${66}^{ \circ  }\mathrm{C}$，${10}\mathrm{\;V}$或${434}\mathrm{\;{mW}}$电压下最高达${125}^{ \circ  }\mathrm{C}$，表明该石墨烯加热器能以低功耗实现高度可控的加热曲线，有效加速解吸过程。

![01965753-5726-75f8-b5cf-d179c2f82f55\_6\_94\_144\_1547\_1062\_0.jpg](images/01965753-5726-75f8-b5cf-d179c2f82f55_6_94_144_1547_1062_0.jpg)

图3. 湿度传感器的制备与表征。A) LIG湿度传感器及加热器制备流程。B) LIG湿度传感器与加热器实物照片及传感器SEM图像。C) LIG传感器的拉曼光谱。D) 湿度以20%间隔从0%升至80%时LIG传感器的归一化电阻变化。每个湿度区间测试后，传感器在60°C、0%湿度下加热以促进LIG电极的水分子解吸。20%、40%、60%和${80}\%$湿度下的标准偏差分别为${4.38} \times  {10}^{-3},{3.948} \times  {10}^{-3},{3.72} \times  {10}^{-3}$和${4.46} \times  {10}^{-3}$。E) 加热器在2-10V工作电压下的温度特性曲线。各电压下温度数据以平均值$\pm$标准偏差表示:2V(34.18$\pm$0.20${}^{ \circ  }$℃)、4V(47.17$\pm$0.48${}^{ \circ  }$℃)、6V(66.78$\pm$0.53${}^{ \circ  }$℃)、8V(89.22$\pm$0.74${}^{ \circ  }$℃)、10V(119.10$\pm$0.99${}^{ \circ  }$℃)。

### 3.4. 无线电路开发与设备集成

如图4A所示，SFFR系统的固件设计采用多层功能架构，以确保湿度传感器、压力传感器阵列监测及自适应贴合调节功能的平稳运行。该固件的功能后端层包括原始传感器数据处理、设备功能执行与反馈控制，以及优化能耗的电源管理模块。为简化通信任务，系统采用低功耗片上系统(SoC)微控制器(MCU)作为主处理单元，集成蓝牙低能耗无线通信与多协议通信层。传感器数据处理层负责处理嵌入式传感器采集的原始数据，而控制层则根据传感器数据与接收指令执行各类功能。

在无线实时SFFR系统中实现压力传感，需集成FDC1004传感器IC元件，通过MCU与双线接口(TWI)进行通信。压力传感器系统搭载板载自适应传感解决方案，可自动校准压力测量值。该校准程序旨在最大限度减少环境波动及呼吸器状态变化对电容测量系统的影响。闭环反馈机制的设计提升了系统精度，实现压力传感设备与安卓应用的主动通信。当压力传感器与便携设备建立连接后，系统会自动校准每个呼吸器的电容值，该过程采用FDC1004固件协议来调整偏移电容与电容增益。

此外，系统集成ADS1292传感器实现LIG电阻传感，该传感器作为低功耗模拟前端(AFE)通过串行外设接口(SPI)工作。惠斯通电桥电路使MCU能精确测量电阻值。最后，伺服电机驱动器通过MCU的通用输入输出(GPIO)引脚传输数字信号来控制电机运行。如图所示，云服务的集成实现了数据实时远程存储，支持大规模数据分析、统计分析的机器学习应用，并能基于历史数据为用户提供个性化反馈。

该设计需要两种具备无线通信功能的柔性印刷电路板(fPCB):一种用于压力传感，另一种用于贴合执行器/LIG湿度传感。图4B展示了用于压力传感器阵列实时数据采集并向指定便携设备无线传输数据的fPCB示意图。该fPCB包含无线数据传输用的微处理器单元(MPU)和蓝牙天线。MPU从两个电容测量IC接收数据，这些IC通过5针柔性扁平电缆(FFC)连接左右压力阵列。FFC的5针与排线形成稳定电气连接，可轻松与PI/铜电极的连接焊盘集成。

同理，贴合执行器/LIG传感器的fPCB也包含相同的MPU和天线组件用于无线数据传输，但如图4C所示，它由伺服电机驱动和湿度传感两个模块组成。电阻测量IC实时监测通过排线与LIG传感接触垫电气连接的LIG电极电阻。集成组件为N95口罩增加${7.5}\mathrm{\;g}$重量，总重达${19.5}\mathrm{\;g}$，增幅${35}\%$。该重量处于日常物品(如眼镜25-75克)的范围内，长时间佩戴不易产生不适。图S7展示了采用白色Ecoflex封装保护的湿度传感/贴合执行器和8通道压力传感无线电路。加热接触垫用于提供电压加热电极实现气体解吸。

图4D展示了集成SFFR的外部视图，包含贴合执行器及两块分别用于压力传感和贴合执行器/LIG湿度传感的fPCB。图S8呈现了SFFR内部结构，中央是LIG湿度传感器，边缘是实时监测呼吸器贴合状态的压力传感器阵列。呼吸器上的微型切口用于连接LIG传感器/加热电极与外部电路。如图S9所示，本研究开发的定制磁吸充电电池可通过磁吸线缆轻松为电路充电。

图4E所示的流程图主要分为自动贴合调节和湿度监测两种模式。在自动贴合模式下，系统实时监测呼吸器周边8个点与用户面部皮肤的接触状态。若出现贴合不良，伺服电机会实时收紧束带直至8个接触点达到理想贴合状态。当通过电机调节实现最佳贴合后，SFFR切换至湿度监测模式。当呼吸器内湿度过高时，伺服电机将松解束带并提示用户摘下呼吸器，随后湿度传感器背面的石墨烯加热器开始加热使水分子从LIG电极解吸。待稳定后持续加热使LIG上的水分子完全解吸，即可重复使用SFFR。

图4F展示了安卓应用界面，可实时监测8点接触数据与呼吸器内部湿度。通过无线连接便携设备的两块电路板，SFFR启动各压力传感器的初始电容校准，以便用户佩戴时追踪电容实时变化。图中$4\mathrm{\;F}$处呼吸器图形周围的色块表示贴合程度:图形用户界面(GUI)中的绿色方块表示压力传感器已校准且贴合良好，深灰色代表过紧，红色与黄色分别表示贴合较差和中等偏差。用户佩戴时，应用会提示校准完成状态并在界面顶部显示实时湿度值。除自动调节功能外，用户还可手动控制左右伺服电机来按需调节束带松紧。图S10提供了安卓应用GUI的更详细说明。

![01965753-5726-75f8-b5cf-d179c2f82f55\_7\_101\_154\_1551\_1405\_0.jpg](images/01965753-5726-75f8-b5cf-d179c2f82f55_7_101_154_1551_1405_0.jpg)

图4. 无线电路开发及器件集成至SFFR系统的过程。A)固件运行逻辑与流程图。B)压力传感电路设计。C)无线贴合驱动、湿度传感及加热电路设计。D)集成多传感器、电路和贴合驱动器的SFFR系统实物照片。E)描述贴合调节与湿度传感闭环反馈控制的流程图。F)安卓应用程序截图，显示实时压力与湿度数据及多种功能。

#### 3.5.系统在人体受试者中的性能现场演示

图5A展示了人体佩戴集成式智能贴合度调节呼吸器(SFFR)的正侧面视图，该装置配备两个无线通信电路和自适应调节执行器。图5B和视频S1验证了本研究的自适应调节模式。视频中平板电脑屏幕显示8个压力传感器由蓝转红，表明所有传感单元均实现无线连接。当受试者佩戴呼吸器出现黄红色反馈时，表明贴合不良。通过伺服电机收紧束带后，所有传感器均显示绿色，实现完美贴合。即使受试者留有胡须(通常影响气密性)，该系统仍能确保与皮肤紧密接触，有效阻断病原体通过界面缝隙渗透。如视频所示，当受试者移动呼吸器时，系统会再次激活自动调节功能。为评估SFFR对不同面部几何形态的适应性，我们招募了具有多样化面部特征的受试者。通过测量面宽、颧鼻距和下颌曲率等人体测量学参数，系统化分析了呼吸器对不同面部尺寸的适配性能。如图5B所示，佩戴时系统自动测量1-8通道的基准电容值作为参考。图5C展示了1-4通道在调节过程中的压力响应:黄色代表欠贴合(电容比$\frac{\Delta C}{{C}_{0}} : 0 - {0.1}$)，绿色表示理想贴合$\left( {\frac{\Delta C}{{C}_{0}} : }\right.$${0.1} - {0.2}$，灰色则对应过贴合$\left( {\frac{\Delta C}{{C}_{0}} : {0.2} - {0.4}}\right)$。视频S2证实该系统不受性别、脸型和尺寸影响，真正实现"一体适用"。当出现过贴合时，用户重新佩戴后系统能基于传感反馈自动优化(视频S3和图S11)。采用PortaCount®呼吸器密合度测试仪8048进行的对比测试显示(设备界面见图S12)，在正常呼吸、深呼吸、摇头和抬头等场景下，SFFR的密合系数(外界颗粒浓度与内部泄漏浓度的比值)平均达186.06，较N95呼吸器提高9.36%(图S13)。性能提升主要源于:(1)自适应调节模式；(2)采用Silbione-Ecoflex生物凝胶包边层，该材料已被证实能降低与皮肤分离所需能量[46]。通过监测"说话"和"做表情"时的1-8通道压力响应(图S14-S15)，系统可实时识别不同状态。图5D显示受试者佩戴$3\mathrm{\;h}$期间LIG湿度传感器的归一化电阻变化:佩戴后湿度急剧上升，后期因水汽积聚增速趋缓。摘下呼吸器后，嵌入传感器的石墨烯加热器在${60}^{ \circ  }\mathrm{C}$启动，加速水分脱附。对照实验(图S16)表明外部湿度变化对内部湿度影响微乎其微。连续使用${24}\mathrm{\;h}$后仅出现轻微压痕(图S17)，证实了长期生物相容性。当前系统存在以下局限:面部适应性调节响应需优化；缺乏基于湿度的松紧度自调节功能；仅能检测湿度，未来需集成多气体传感；一次性设计造成电子元件浪费，建议采用可拆卸式模块化设计；LIG湿度传感器的加热脱附工艺存在安全隐患，需开发更安全的湿度检测方案。

![01965753-5726-75f8-b5cf-d179c2f82f55\_8\_108\_1032\_1540\_999\_0.jpg](images/01965753-5726-75f8-b5cf-d179c2f82f55_8_108_1032_1540_999_0.jpg)

图5. SFFR系统在人体受试者中的性能现场演示。A) 受试者佩戴SFFR系统的正面与侧面视图。B) 系统自适配调节能力演示，平板应用程序显示适配不良警告。C) 系统在(B)阶段自适配调节过程中四个通道的压力传感响应。D) 受试者佩戴呼吸器时(绿色区域)及卸下后以60${}^{ \circ  }\mathrm{C}$加热促进解吸过程时(粉色区域)，系统测量的湿度传感曲线。(关于图例中颜色引用的解释，请参阅本文网络版。)

## 4. 结论

本文介绍了一种智能过滤式面罩呼吸器(SFFR)系统，该系统采用多种生物相容性材料开发基于激光诱导石墨烯(LIG)的湿度传感器和压力传感器阵列，可在调节呼吸器内部空气质量的同时实现自适配调节，以获得最佳呼吸器性能。基于介电弹性体海绵的压力传感器分布于呼吸器边缘，通过检测电容变化(检测限低至${0.23}\mathrm{{kPa}}$)实时监测适配状态，并具备${0.7}\mathrm{\;s}$的快速动态响应和${0.55}\mathrm{\;s}$的恢复速率。系统集成有基于LIG的湿度传感器，可在相对湿度${20}\%$条件下以${0.131}\%$的超低滞后特性检测呼吸器内部湿度上升，嵌入式石墨烯加热器可加速水解吸速率，使使用后湿度水平快速稳定。与N95口罩相比，得益于自适配调节模式和弹性衬里，SFFR的整体适配系数高出10.12%。在多名不同性别、脸型、尺寸及有无胡须的受试者身上进行的自适配调节模式实时演示，突显了该系统普适性能。该可穿戴系统解决了需保持面部密封的专业人员面临的重要挑战，具有突破现有呼吸器局限的潜力，为开发集成生物传感器提供了重要参考。

## 作者贡献声明

Kangkyu Kwon:初稿撰写、形式分析、数据整理、概念化。Yoon Jae Lee:文稿审阅与编辑、软件开发、形式分析、数据整理。Yeongju Jung:形式分析、数据整理。Ira Soltis:形式分析、数据整理。Yewon Na:数据整理。Lissette Romero:数据整理。Myung Chul Kim:数据整理。Nathan Rodeheaver:数据整理。Hodam Kim:数据整理。Chaewon Lee:数据整理。Seung-Hwan Ko:文稿审阅与编辑、资源获取、资金支持、形式分析。Jinwoo Lee:初稿撰写、资源获取、资金支持、形式分析、数据整理。Woon-Hong Yeo:文稿审阅与编辑、初稿撰写、项目监督、资源协调、项目管理、资金支持、概念化。

## 利益冲突声明

作者声明以下可能构成潜在竞争利益的财务关系/个人关系:佐治亚理工学院有一项美国专利申请正在审理中。

## 数据可用性

数据可根据请求提供。

## 致谢

作者感谢美国疾病控制与预防中心(合同编号:200-2021-10546)和佐治亚理工学院物质与系统研究所WISH中心的支持。我们特别感谢与疾病预防控制中心同仁Ziqing Zhuang、William King和Susan Xu富有成效的讨论。部分工作在美国国家纳米技术协调基础设施(NNCI)成员单位——佐治亚理工学院物质与系统研究所完成，该机构获得美国国家科学基金会支持(ECCS-2025462)。本研究同时受韩国国家研究基金会(NRF)资助(NRF-2021R1A2B5B03001691, RS-2024-00411952)。

## 附录A. 补充材料

本文补充材料详见https://doi.org/10.1016/j.biomaterials.2024.122866。

## 参考文献

[1] 世界卫生组织, 冠状病毒病(COVID-19), 2020年。(2020年10月12日访问)

[2] J. Howard, A. Huang, Z. Li等，《口罩对抗COVID-19的证据综述》，载《美国国家科学院院刊》第118卷第4期(2021年)。

[3] N.H. Leung, D.K. Chu, E.Y. Shiu等，《呼气中的呼吸道病毒排放及口罩防护效果》，载《自然·医学》第26卷第5期(2020年)676-680页。

[4] F. Seidi, C. Deng, Y. Zhong等，《功能化口罩:抗击COVID-19及未来大流行的强力材料》，载《Small》第17卷第42期(2021年)2102453。

[5] E. O'Kelly, A. Arora, S. Pirog等，《N95、KN95、医用外科口罩与布口罩的贴合度比较及适配检查准确性评估》，载《PLoS One》第16卷第1期(2021年)e0245688。

[6] A. Regli, A. Sommerfield, B. von Ungern-Sternberg等，《N95/FFP2/FFP3口罩适配测试的作用:叙述性综述》，载《麻醉学》第76卷第1期(2021年)91-100页。

[7] C.E. Rodriguez-Martinez, M.P. Sossa-Briceño, J.A. Cortés等，《N95过滤式面罩呼吸器的去污与重复使用:文献系统综述》，载《美国感染控制杂志》第48卷第12期(2020年)1520-1532页。

[8] F.M. Blachere, A.R. Lemons, J.P. Coyle等，《提升源头控制性能的口罩贴合度改良方案》，载《美国感染控制杂志》第50卷第2期(2022年)133-140页。

[9] D. Janson, B. Clift, V. Dhokia，《COVID-19疫情期间医护人员的个人防护装备适配性》，载《应用工效学》第99卷(2022年)103610。

[10] Y. Li, H. Tokura, Y. Guo等，《佩戴N95与医用外科口罩对心率、热应激及主观感受的影响》，载《国际职业与环境健康档案》第78卷(2005年)501-509页。

[11] J. Suo, Y. Liu, C. Wu等，《集成宽带纳米复合传感器的智能口罩用于多相呼吸活动追踪》，载《先进科学》第9卷第31期(2022年)2203565。

[12] Q. Lu, H. Chen, Y. Zeng等，《基于摩擦纳米发电机的智能呼吸监测口罩》，载《纳米能源》第91卷(2022年)106612。

[13] Z. Liu, Z. Li, H. Zhai等，《基于多功能织物的高灵敏度可拉伸应变传感器用于呼吸监测与识别》，载《化学工程杂志》第426卷(2021年)130869。

[14] H.C. Bidsorkhi, N. Faramarzi, B. Ali等，《可穿戴石墨烯智能口罩用于实时人体呼吸监测》，载《材料设计》(2023年)111970。

[15] 钟健、李哲、高桑真、井上大介、桥爪大辅、蒋志、石洋、欧力、纳伊姆·M.O.G.、梅津聪，基于超薄压力传感器的智能口罩用于呼吸状况无线监测，《先进材料》34卷6期(2022) 2107758。

[16] 斯里克里希纳卡、达西·R.M.、贾纳·S.K.、阿胡贾·T.、库马尔·J.S.、纳加尔·A.、基尼·A.R.、乔治·B.、普拉迪普·T.，面向手机端持续呼吸监测的经济型导电织物智能口罩，《ACS欧米伽》7卷47期(2022) 42926-42938。

[17] 曼钱达·A.、李·K.、波兹南斯基·G.D.、哈萨尼·A.，基于物联网传感器融合的PPE口罩自动调节，《传感器》23卷3期(2023) 1711。

[18] 金南、魏·J.L.J.、应杰、张宏、文·S.K.、崔杰，面向医护人员的定制化智能医用口罩，《IEEM》IEEE(2020) 581-585。

[19] 叶志、凌宇、杨明、徐阳、朱磊、严哲、陈培毅，用于无线咳嗽与佩戴监测的可呼吸、可重复使用且零功耗的智能口罩，《ACS纳米》16卷4期(2022) 5874-5884。

[20] 洛克·R.、卡拉贾利·H.、瓦罗尔·A.、卡姆利·U.、伊尔马兹·E.，基于智能口罩设计的电阻温度检测器制备与表征，《先进制造技术杂志》122卷1期(2022) 147-158。

[21] 塞尔瓦达斯·S.、保罗·J.J.、贝拉玛丽·I.T.、帕克亚瓦西·I.S.V.、高塔姆·S.，支持物联网的智能口罩用于COVID19疫情检测，《健康科技》12卷5期(2022) 1025-1036。

[22] 埃斯科贝多·P.、费尔南德斯-拉莫斯·M.、洛佩斯-鲁伊斯·N.、莫亚诺-罗德里格斯·O.、马丁内斯-奥尔莫斯·A.、佩雷斯·德瓦尔加斯-桑萨尔瓦多·I.、卡瓦哈尔·M.、卡皮坦-瓦列维·L.、帕尔马·A.，用于无线CO2监测的智能面罩，《自然通讯》13卷1期(2022) 72。

[23] 巴塔查里亚·N.、辛格·S.、高希·R.、班纳吉·A.、阿迪卡里·A.、哈尔德·A.、高斯瓦米·M.、查托帕迪亚·A.、蒙达尔·P.、纳托·S.S.，舒适卫生呼吸用智能主动呼吸器研发，《物理流体》34卷5期(2022) 051901。

[24] 穆巴洛克·A.W.、萨米贾亚尼·O.N.、拉赫马蒂亚·S.，支持后疫情时代新常态活动的基于物联网的智能教室及口罩检测系统，《ISESD》IEEE(2022) 1-6。

[25] 贾拉贾莫尼·H.M.、费尔南德斯·R.E.，用于无电池智能口罩实时湿度检测的多孔硅基微光谱单元，《传感器》IEEE(2022) 1-4。

[26] 普拉巴·K.、纳塔拉杰·B.、斯瓦蒂·P.、塔拉尼·V.、维诺拉·A.，基于物联网的健康监测智能口罩，《ICOSEC》IEEE(2022) 568-572。

[27] 段志、张博、张明、袁哲、江洋、邰海，基于NaCl/海藻酸钠湿敏电解质和$\mathrm{{Cu}}/\mathrm{{Zn}}$电极的高性能电化学发电湿度传感器，用于可视化湿度指示与呼吸模式检测，《传感器与执行器B:化学》409卷(2024) 135585。

[28] 张明、段志、张博、袁哲、赵琦、江洋、邰海，电化学湿度传感器驱动的自供电无线湿度检测系统，《纳米能源》115卷(2023) 108745。

[29] 祖煜、胡杰、杨明、段志远、张明、袁哲、江宇、邰浩，基于二硫化钨纳米片的电化学发电湿度传感器，《传感器与执行器B:化学》405卷(2024年)135325页。

[30] 段志远、李杰、袁哲、江宇、邰浩，基于磷酸锆纳米片薄膜的宽量程高响应电容式湿度传感器，《传感器与执行器B:化学》394卷(2023年)134445页。

[31] 金赫、金恩、崔昌、韦浩·杨，可穿戴健康监测设备用柔性干电极研究进展，《微机械》13卷4期(2022年)629页。

[32] 林赫荣、金赫秀、卡齐尔、权永泰、郑正元、韦浩·杨，医疗/能源/环境领域可穿戴柔性混合电子器件的先进软材料与传感器集成应用，《先进材料》32卷15期(2020年)1901924页。

[33] 赫伯特、金正赫、金永秀、李赫明、韦浩·杨，用于医疗保健与人机交互的软材料柔性混合电子器件，《材料》11卷2期(2018年)187页。

[34] 河康昊、许赫、李哲、吕宁，柔性电容式压力传感器:发展趋势、挑战与前景，《ACS纳米》16卷3期(2022年)3442-3448页。

[35] 黄强、江宇、段志远、吴越、袁哲、张明、邰浩，离子梯度诱导自供电柔性压力传感器，《化学工程杂志》490卷(2024年)151660页。

[36] 李涛、段志远、黄强、杨浩、袁哲、江宇、邰浩，通过集成表面微结构与增强介电常数构建简易低成本
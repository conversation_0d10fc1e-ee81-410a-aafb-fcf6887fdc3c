Fully portable continuous real-time auscultation with a soft wearable stethoscope designed for automated disease diagnosis
用于自动化疾病诊断的柔性可穿戴听诊器实现全便携式持续实时听诊

李成勋${}^{1,2}$等人, Yun-<PERSON><PERSON> Kim${}^{2,3}$等人, Min-<PERSON><PERSON> Yeo${}^{4}$等人, <PERSON>ood${}^{2,3}$, <PERSON>${}^{2,5}$, Chaeuk Chung${}^{6}$, Jun Young Heo${}^{7}$, Yoon<PERSON><PERSON> Kim${}^{6}$, 郑成洙${}^{8 * }$, Woon-Hong Yeo${}^{2,3,5,9 * }$

现代听诊技术采用数字听诊器，在声音记录和可视化方面提供了优于传统方法的解决方案。然而现有数字听诊器体积庞大且与皮肤贴合度差，难以实现持续听诊。此外，刚性材质导致的运动伪影会产生摩擦噪声，造成诊断误差。本研究报道了一种基于柔性可穿戴系统的实时无线持续听诊技术，作为多种疾病的定量诊断工具。该柔性设备能以极低噪声持续监测心肺音，并对实时信号异常进行分类。通过多病例对照临床研究证明，这种搭载嵌入式机器学习算法的可穿戴听诊方法在自动诊断四种肺部疾病(爆裂音、哮鸣音、喘鸣音和干啰音)时准确率达95%。该柔性系统还通过监测睡眠呼吸紊乱展示了在家庭睡眠呼吸暂停检测中的应用潜力。

## 引言

慢性阻塞性肺疾病(COPD)和心血管疾病(CVD)(1)是全球主要致死原因，每年造成近百万死亡(2)。COPD和CVD是导致心肺功能障碍的系列疾病统称，会限制血流引发呼吸困难与严重不适(3)。令人震惊的是，80%的COPD死亡发生在中低收入国家(LMICs)，这些地区医疗资源可及性和现有医疗设备负担能力的不足，限制了长期追踪这些进展性疾病发展的可行性。对于COPD和CVD患者的诊断护理，精确听诊有助于早期诊断和治疗反应评估(4,5)。哮鸣音对这些疾病的诊断监测至关重要(6)。爆裂音对诊断肺炎、特发性肺纤维化和肺水肿具有关键意义(5)。喘鸣音提示上呼吸道严重阻塞，有助于患者急救处理。心音也为诊断各类瓣膜性心脏病和心力衰竭提供重要信息。若出现S1和S2心音分裂，可能提示瓣膜疾病、心律失常和肺动脉高压等CVDs(7,8)。若听到病理性S3或S4心音，则可能表明存在瓣膜疾病、心力衰竭和急性冠状动脉疾病(9)。

听诊因其无创、快速、信息丰富且成本低廉的特点，始终是医疗领域最基础关键的诊断方法。尽管胸部CT和超声心动图等影像诊断技术已广泛应用于临床，听诊仍是基础诊断工具，在中低收入国家尤其如此(10)。但传统听诊器存在根本性局限:多数无法记录听诊声音，难以与其他医护人员共享；且听诊分析结果高度依赖临床医生的知识经验，导致部分严重呼吸系统或心脏疾病漏诊误诊(5)。当前由于人口老龄化加剧、空气污染恶化及各类传染病影响，COPD和CVD发病率与社会经济负担持续攀升。通过精准听诊实现早期诊断和准确监测变得愈发关键，听诊技术的改进需求迫在眉睫。

数字听诊器通过将声学声音转化为电信号并实时记录，辅助远程医疗诊断，能放大传统听诊器难以捕捉的微弱声响(11)。在日常诊疗中可替代双耳听诊器使用。这类设备还可结合计算机软件提升诊断能力，但目前尚无法实现基于信号处理的精准诊断(12)。虽然信号图谱能量化测量数据、减少不同医师诊断的主观差异，但听诊器在胸背部放置位置与压力的差异仍会导致数据采集时的摩擦噪声和人为误差。这对缺乏专业培训的家用患者尤为突出。现有数字听诊器的笨重设计亟需改进为更人性化的形态。需注意的是，数字听诊器本身不具备病理或异常音识别功能，仅作为声音记录装置，某些情况下可显示心音图。要实现自动诊断，必须开发配套计算机算法。多数研究采用单点胸部录音，但精细诊断常需多听诊位点采集，这可能增加误差(12)。开发更经济、精准、可穿戴的数字听诊设备将提升远程医疗可及性，通过持续监测实现心血管与呼吸系统疾病的早期发现。

本文提出一种基于柔性电子技术、柔性机械结构与软封装的新型可穿戴软听诊器(SWS)系统，用于动态心肺听诊，实现持续心血管与呼吸监测。重点在于设备设计如何确保日常活动中准确采集心肺数据(13)以诊断各类肺部异常。研究展示了以下成果:通过小波去噪提升信噪比(SNR)、精简电路使设备更紧凑、训练机器学习模型从设备数据中识别喘鸣音(14)、干啰音(15)、哮鸣音(16)和湿啰音(17)。同步开发了用户友好型移动应用，可记录心肺音、实时追踪显示信号、自动诊断异常肺音，并安全远程上传数据至同步本地存储器。

## 结果

## SWS的设计、架构与机械特性

图1总结了软体可穿戴系统(SWS)的设计概览与核心功能。通过纳米材料打印、系统集成与软质材料封装技术的结合，该系统实现了用于远程患者心肺听诊的微型化柔性穿戴方案。图1A所示的SWS具有极小的外形尺寸和机械柔韧性，能实现与皮肤的紧密贴合，支持患者自主操作进行远程持续监测，无需医患直接接触。其软机械特性包含弹性封装层(内衬硅胶厚度$\left( {{300\mu }\mathrm{m}}\right.$、杨氏模量$4\mathrm{{kPa}}$，详见视频S1)(18)。该设计通过导电水凝胶耦合层将设备轻柔贴附于胸背部曲面皮肤，实现心肺音采集。硅胶背衬使设备可重复使用数日且保持声学检测质量。图1B示意图展示了多层软材料与电子元件的密集堆叠结构，包括麦克风传感器、柔性薄膜电路、可充电电池及蓝牙低功耗(BLE)无线传输模块(电路设计详见附图S1-S2)。系统采用微机电(MEMS)麦克风，其微型振膜适合声音采集，经模数转换后通过BLE芯片实时传输数据。采集后的声音信号通过降噪算法过滤杂音并进行分类标记(19)。关键设计在于将麦克风与核心电路隔离，增强皮肤接触稳定性以降低持续听诊噪声。器件制备流程见附图S3，芯片组装过程如附图S4所示。集成化SWS可连续10小时无线传输心肺音数据(附图S5)，由微型锂聚合物电池供电(容量<b2></b0>)。电池电极与电路焊盘通过钕磁体实现导向连接。计算有限元分析与实验验证了设备在模拟呼吸周期下的皮肤弯曲力学特性(20)(图1C-F)。器件机械可靠性测试如附图S6所示，低模量弹性体封装系统在100次循环测试中电阻波动小于30毫欧，拉伸/弯曲总电阻变化分别为0.41/0.71毫欧。图1G展示了基于机器学习的疾病自动诊断流程:通过定制安卓应用(附图S7)实时采集声音，经卷积神经网络(CNN)预处理与分类。例如吸气粗湿啰音提示慢阻肺，S3/S4心音预示心功能异常(21)。该便携系统为减少患者住院监测提供了创新解决方案(表1)。

## SWS运动伪影的力学原理、优化与控制

为实现高质量、低噪声的听诊，必须确保可穿戴麦克风系统与皮肤保持紧密接触，即使在日常活动时也不例外。图2中的一系列实验研究揭示了SWS在确保皮肤贴合接触和控制运动伪影方面的创新性。图2A照片对比了微型化SWS与商用数字听诊器[ThinkLabs One (TLO)]，后者的最大局限在于材料与电子封装的刚性结构，导致在人体曲面皮肤上产生明显气隙和分层(图2B)。传统经典听诊器(图S8)如预期所示具有相似形态和刚性。而纤薄柔性的SWS则能与皮肤完美贴合。移除顶部外壳后，麦克风岛单元展现出无可见气隙的优质皮肤接触，实现高保真录音。图2C实验通过四种薄膜基底筛选出能提供高压力以增强设备-皮肤接触的最佳背衬材料，使用微型压力传感器(SingleTact)测量了包括硅胶基底、3M 2476P胶带、3M Tegaderm胶带和3M Micropore胶带的压力值(实验设置详见S9图)。当四种设备检测心音时，硅胶基设备以0.6以上的归一化振幅呈现最佳S1音质(图2D)。临床上S1对应脉搏，通常为单一心音，因二尖瓣与三尖瓣几乎同步关闭。图2E曲线图通过四次试验汇总各设备信噪比，清晰显示硅胶案例以${16}\mathrm{\;{dB}}$ 的平均值保持最高接触质量。图2F补充实验对比了

![0196347a-a557-71e6-b3cc-067fd499535a\_2\_339\_176\_1144\_1444\_0.jpg](../../../../../6、知识库/文献知识/4、柔性系统/实验借鉴/国际/团队/Woon-Hong%20Yeo/2022/用于自动化疾病诊断的柔性可穿戴听诊器实现全便携式持续实时听诊/images/0196347a-a557-71e6-b3cc-067fd499535a_2_339_176_1144_1444_0.jpg)

图1. SWS的设计架构与力学特性。(A)远程监测示意图展示手指与胸部佩戴特写(下)，移动设备显示三日心肺听诊实时数据曲线(右)，右下角为无接触日常活动场景。(B)SWS多层材料沉积的爆炸视图。(C)SWS中20%拉伸互连结构图像，(D)对应(C)的有限元分析结果。(E)SWS180°弯曲照片，(F)基于(E)的循环弯曲FEA模拟。(G)通过机器学习实现疾病自动客观诊断的流程示意图:实时采集的异常音经预处理和算法分类后，通过移动端应用程序输出结果。

科学进展|研究论文 SWS与商用TLO的录音性能对比。将两种设备固定于胸部，健康受试者在站立行走等活动中持续录制心音超过$5\mathrm{\;{min}}$ 。放大视图(图2G)显示:具备皮肤贴合特性的SWS(上图)清晰呈现S1/S2峰，而商用设备(下图)因刚性结构及重量产生显著运动伪影。防水测试(图S10)表明SWS可在淋浴等日常场景中使用，即便水流冲击仍能准确捕捉S1/S2峰。该设备还具有优异透气性，基底材料的高水蒸气透过率促进皮肤呼吸。图S11数据显示SWS在不同膜材中气体渗透性最佳，可长期使用而不损伤皮肤。亲肤性设计使受试者能连续三天佩戴同一设备进行办公、商务会谈、睡眠和运动等多场景监测(图S12)，期间粘附值虽有变化(图S13)但未观察到不良反应。

表1. 采用麦克风的可穿戴数字听诊器性能对比。MIO指机械性肠梗阻。

<table><tr><td>Reference</td><td>Continuous cardiopulmonary monitoring</td><td>Controlled motion artifact</td><td>SNR of cardiac signals (dB)</td><td>Activity levels</td><td>Clinical study</td><td>Number of patients</td></tr><tr><td>This work</td><td>Yes (more than 10 hours up to 3 days with a 150-mAh battery)</td><td>Yes</td><td>14.8</td><td>Daily activities with jogging</td><td>Patients with lung disease</td><td>20 (12 crackle, 1 rhonchi, 4 wheeze, and 3 stridor)</td></tr><tr><td>(33)</td><td>No</td><td>No</td><td>1.76</td><td>Stationary</td><td>-</td><td>-</td></tr><tr><td>(34)</td><td>No</td><td>No</td><td>10.0</td><td>Stationary</td><td>-</td><td>-</td></tr><tr><td>(35)</td><td>No</td><td>No</td><td>6.02</td><td>Stationary</td><td>-</td><td>-</td></tr><tr><td>(36)</td><td>No</td><td>No</td><td>10.0</td><td>Stationary</td><td>-</td><td>-</td></tr><tr><td>(37)</td><td>No</td><td>No</td><td>6.02</td><td>Stationary</td><td>-</td><td>-</td></tr><tr><td>(38)</td><td>No</td><td>No</td><td>7.00</td><td>Stationary</td><td>Patients with MIO and paralytic ileus</td><td>2 (1 MIO and 1 paralytic ileus)</td></tr><tr><td>(39)</td><td>No</td><td>No</td><td>7.36</td><td>Stationary</td><td>-</td><td>-</td></tr><tr><td>(19)</td><td>No</td><td>No</td><td>6.19</td><td>Stationary</td><td>-</td><td>-</td></tr><tr><td>(40)</td><td>No</td><td>No</td><td>10.0</td><td>Stationary</td><td>Patients with pneumonia</td><td>5 (pneumonia)</td></tr><tr><td>(41)</td><td>No</td><td>No</td><td>3.98</td><td>Stationary</td><td>Patients with wheeze</td><td>40 (wheeze)</td></tr><tr><td>(42)</td><td>No</td><td>No</td><td>10.0</td><td>Stationary</td><td>-</td><td>-</td></tr><tr><td>(13)</td><td>No</td><td>No</td><td>6.99</td><td>Stationary</td><td>-</td><td>-</td></tr><tr><td>(43)</td><td>No</td><td>No</td><td>9.03</td><td>Stationary</td><td>-</td><td>-</td></tr></table>

## 日常生活中完全便携式持续心音监测

各类日常活动产生的不同噪声源会对SWS(软性可穿戴听诊器)的声音记录造成负面影响。传统手动听诊器和市售数字听诊器均存在显著噪声与运动伪影问题，因此听诊检查仅限于患者静息状态下进行。与这些设备不同，SWS凭借其设备形态与稳定的皮肤接触质量，能有效处理并控制运动伪影。图3A展示受试者模拟多种现实场景(包括静止站立对照、与人交谈、步行及慢跑)的实验组。本研究旨在揭示刚性电子电路对声音采集质量的影响。由于市面无商用可穿戴听诊器，我们自制了刚性版本设备与软性设备进行对比(图3A右图)。图3B汇总了两款设备在不同活动(对照、交谈、步行、慢跑)中的心音测量数据:软性设备呈现受控运动伪影数据且噪声可忽略，而刚性设备因运动导致分层而数据失真。图3C详细信号显示步行和慢跑动作对刚性设备产生明显噪声干扰，软性设备则几乎不受影响。图3D对比安装在柔性皮肤模型上的两款设备(上:软性；下:刚性)，可见软性设备保持贴合接触，刚性设备出现分层现象(左下及右下图为去除顶部封装后的放大结构视图)。

控制运动伪影的另一关键要素是确保麦克风内部振膜与皮肤间气隙变化最小化(图3E)，因该气隙作为声学电容将压力波转换为电信号(22)。如前所述，软性设备通过柔性凝胶层实现皮肤共形贴合，抵御各种活动中的气隙变化。而传统刚性设备在受试者步行及下楼梯时仍受运动噪声干扰(详见影片S1的对比)。刚性设备持续分层会放大皮肤-振膜气隙电容变化，因吸声层过厚导致信号质量下降。这些因素使运动伪影噪声最大化(影片S2采样音频)，造成心肺音记录质量低下。相反，具备优异皮肤接触性的软性设备能最小化表皮与麦克风振膜间的声阻抗。通过初级截止频率的附加滤波可消除临床环境中由运动、语音及提示音产生的高频噪声(22)。

![0196347a-a557-71e6-b3cc-067fd499535a\_4\_342\_171\_1142\_1152\_0.jpg](../../../../../6、知识库/文献知识/4、柔性系统/实验借鉴/国际/团队/Woon-Hong%20Yeo/2022/用于自动化疾病诊断的柔性可穿戴听诊器实现全便携式持续实时听诊/images/0196347a-a557-71e6-b3cc-067fd499535a_4_342_171_1142_1152_0.jpg)

图2. SWS的运动伪影力学原理、优化与控制。(A)皮肤模型上SWS与商用设备(TLO数字听诊器)的对比照片。(B)商用刚性听诊器(左)因45°曲率与皮肤分层接触，SWS则呈现紧密贴合的对比。(C)使用不同生物相容性粘合剂(硅胶、3M 2476P、3M Tegaderm及 micropore)对麦克风岛施加压力的差异。(D)不同粘合剂采集心音S1峰的时序图与归一化振幅。(E)基于(D)中S1峰计算的信噪比(四次试验)。(F)SWS与商用设备(TLO)佩戴于胸部时的时序对比图，受试者进行站立、步行等活动时的录音。(G)步行所致噪声峰局部放大图:皮肤共形接触的SWS(上图)清晰显示S1/S2峰，商用设备(下图)的步频噪声相对心音被放大。

## 心肺音监测中的设备性能

心音由心脏瓣膜开闭产生。二尖瓣与三尖瓣关闭形成S1音，此过程称为收缩期；肺动脉瓣与主动脉瓣关闭时(即舒张期)产生更响亮的S2音(23)。这些心音频率通常在20至${220}\mathrm{\;{Hz}}$赫兹之间，可被高灵敏度麦克风捕获(影片S3)。图4数据汇总展示了SWS设备高质量记录心音及肺音的性能:当设备置于胸部(Botkin-Erb点，图4A)时，能清晰捕捉并区分S1与S2音(图4B)。所采集的振幅、波形、S1-S2间距及相对大小等参数，用于开发生成个体化特征的机器学习算法。图4C声谱图通过颜色显示频率能量，图中黄色高亮部分呈现S1与S2波，其频率范围如预期在${110}\mathrm{\;{Hz}}$赫兹附近。总体而言，这款柔性可穿戴设备在声音高质量检测中表现卓越，能识别手动听诊难以辨别的细微异常音变或原始数据中不可见的声学变化。图4D韦尔奇功率谱密度图显示${90} - \mathrm{{to}} - {150}\mathrm{\;{Hz}}$范围内的峰值能量。最后通过时频分析法(图4E持续谱)测量信号中特定频率的时间占比，时间百分比越高且颜色越亮，表明该时段信号中特定频率强度越大。与声谱图相比，更明亮的颜色代表环境噪声干扰，而目标心音频率(20-180赫兹范围)的聚集曲线分布更为稀疏。

![0196347a-a557-71e6-b3cc-067fd499535a\_5\_341\_169\_1141\_1175\_0.jpg](../../../../../6、知识库/文献知识/4、柔性系统/实验借鉴/国际/团队/Woon-Hong%20Yeo/2022/用于自动化疾病诊断的柔性可穿戴听诊器实现全便携式持续实时听诊/images/0196347a-a557-71e6-b3cc-067fd499535a_5_341_169_1141_1175_0.jpg)

图3. 日常生活中完全便携式心音连续监测。(A)展示站立对照、交谈、步行及慢跑等日常活动的系列照片，右图为SWS与传统刚性设备对比。(B)两种设备在不同活动(对照、交谈、步行、慢跑)下的心音测量曲线:SWS数据受运动伪影控制且噪声可忽略，而刚性设备因运动剥离导致数据失真。(C)心音测量细节数据:步行与慢跑运动使刚性设备产生显著噪声，SWS则几乎不受影响。(D)设备对比照片:上排为贴附于柔性皮肤模型的SWS(贴合良好)与下排刚性设备(出现分层，左图)；右图为去除顶部封装后的器件结构放大视图。(E)SWS的皮肤共形接触设计细节，可最大限度减少皮肤与振膜间气隙变化。

此外，同一设备被用于背部(右下肺叶；图4F)的肺音测量。肺音由空气在肺部支气管树中的进出流动产生。胸腔形状、骨骼数量、皮肤及脂肪层厚度以及肌肉密度均会影响肺音的音质与音高(24)。肺音频率范围在100至${2500}\mathrm{\;{Hz}}$赫兹之间，气管音最高可达${4000}\mathrm{\;{Hz}}$赫兹(视频S4)(24)。尽管存在这些变量，SWS的灵敏度表现卓越，能清晰记录并检测各类呼吸问题(24)。与心音分析类似，SWS测量的肺音需经过图4(G至J)所示的多重分析流程。我们还对心肺音进行了快速傅里叶变换(图S14汇总)，显示频域中离散变换值以检测0至$2\mathrm{{kHz}}$赫兹的功率。为验证可靠性，我们将SWS记录数据与商用数字听诊器(TLO；图S15)进行对比。在同步声音检测实验中，两种设备均安装在带有嵌入式扬声器的仿生皮肤模型上，播放20至${750}\mathrm{\;{Hz}}$赫兹的声音。总体而言，SWS在采集低频范围(自${36}\mathrm{\;{Hz}}$赫兹起)表现优于TLO的${48}\mathrm{\;{Hz}}$赫兹。

![0196347a-a557-71e6-b3cc-067fd499535a\_6\_236\_177\_1355\_1200\_0.jpg](../../../../../6、知识库/文献知识/4、柔性系统/实验借鉴/国际/团队/Woon-Hong%20Yeo/2022/用于自动化疾病诊断的柔性可穿戴听诊器实现全便携式持续实时听诊/images/0196347a-a557-71e6-b3cc-067fd499535a_6_236_177_1355_1200_0.jpg)

图4. 设备在心肺音监测中的性能表现。(A) SWS安装在胸部(鲍特金-埃尔布点)进行心音检测的照片。(B) 健康受试者5秒窗口心音的时间序列图。(C) 时间序列图的频谱图，显示测量信号的频率范围。(D) 0至500Hz心频范围的韦尔奇功率谱密度(PSD)图。(E) 心音持久谱图，显示20至180Hz信号中心频出现的时间百分比。与频谱图相比， brighter颜色代表周围环境中的噪声干扰。(F) SWS安装在背部(右下肺叶点)进行肺音检测的照片。(G) 正常呼吸健康受试者5秒窗口肺音的时间序列图。(H) 肺音信号频率范围的频谱图。(I) 0至2kHz肺频范围的韦尔奇功率谱密度图。(J) 肺音持久谱图，显示450至${1350}\mathrm{\;{Hz}}$Hz信号中肺频出现的时间百分比。

## 用于分析心肺音的小波去噪算法

心音与肺音信号的小波变换及噪声滤波处理在本研究中至关重要，因为麦克风会捕获来自人体及周围环境的所有声音。采用阈值算法的小波去噪是抑制数字信号噪声最有效的方法之一。在听诊中，确定心音与肺音的小波阈值去噪阈值尤为关键:阈值过低可能无法完全消除噪声系数，而过高阈值会使更多系数归零，导致分解数据特征丢失(23)。图5A的流程图展示了小波去噪算法如何通过采集声音的分解与重构实现降噪。本研究使用两组滤波器组对听诊数据进行环境噪声去除，包括分析滤波器组与合成滤波器组(图S16A)。分析滤波器将输入的心肺音分解为降采样子带，合成滤波器组则在升采样后重构原始心肺音数据。音频信号经算法读取后，会向原始信号添加高斯噪声形成含噪信号。计算含噪信号的信噪比(SNR)与均方根误差(RMSE)后，小波阈值根据SNR与RMSE比值确定，并参考噪声强度与分解阶段。该阈值被应用于分解后的小波系数，肺音听诊采用软阈值处理(24)。软阈值能在重构信号与原始信号间保持稳定差异，使尖锐声音平滑化。最后步骤是利用输入合成滤波器组的软阈值小波系数重构肺音信号。去噪后的呼吸数据如图5B所示，可捕捉测量肺音中的各类异常，并以$\pi$弧度/样本显示各样本归一化频率。去噪肺音的小波特征总结于图S16B，心音分析流程与之相同(图S16C)。我们将小波去噪算法应用于多组患者数据集，通过软穿戴系统(SWS)与商用听诊器(3M Littmann)进行测量对比。图5(C-F)系列汇总图表显示肺病患者(含湿啰音、干啰音、哮鸣音、喘鸣音)的SNR计算结果，左列为SWS采集数据，右列为3M设备测量数据。图S17的滤波频谱图进一步对比了新旧设备的去噪信号质量。实验研究表明，软穿戴系统在肺音及各类病症异常的检测性能上优于传统设备。表S1汇总了两款设备的SNR测量数据与患者症状信息(影片S5-S8播放各类病症声音)。

![0196347a-a557-71e6-b3cc-067fd499535a\_7\_281\_169\_1262\_1135\_0.jpg](../../../../../6、知识库/文献知识/4、柔性系统/实验借鉴/国际/团队/Woon-Hong%20Yeo/2022/用于自动化疾病诊断的柔性可穿戴听诊器实现全便携式持续实时听诊/images/0196347a-a557-71e6-b3cc-067fd499535a_7_281_169_1262_1135_0.jpg)

图5. 心肺音分析用小波去噪算法概览。(A)算法流程图展示小波去噪通过声音分解与重构的工作原理。(B)测量肺音各类异常的尺度图，以$\pi$弧度/样本显示归一化频率，所有样本数据输入去噪算法的小波分解环节。(C)湿啰音患者数据趋势图对比:左列为去噪后的SWS数据，右列为3M Littmann商用听诊器测量结果，附相应SNR值。(D)干啰音患者数据趋势图与SNR值。(E)哮鸣音患者数据趋势图与SNR值。(F)喘鸣音患者数据趋势图与SNR值。

## 基于CNN机器学习的自动疾病诊断系统(集成于软穿戴听诊器)

在必要时，医师或临床医生会使用传统数字听诊器采集患者的声学信号。但如图6A所示，这种方式存在对采集声音进行主观听诊分析的固有缺陷。例如，诊治呼吸系统疾病的肺科医生可能因录音质量或时长差异而给出不同诊断意见。最新研发的智能穿戴听诊系统(SWS)具有关键优势:能进行噪声可控的高质量声音连续实时录制、定量数据分析，并基于机器学习实现疾病自动客观分类(如爆裂音、干啰音、哮鸣音、喘鸣等肺部异常)。图6B中各类肺部异常的小波尺度图展示了样本序列的归一化频率分布，单位为$\pi$弧度/样本。数据按75%训练集与25%测试集划分，确保无交叉重叠，并作为五折交叉验证的一部分重复四次测试。每个样本被聚类为2秒数据包输入基于卷积神经网络(CNN)的机器学习系统。为同步展示小波变换心音图，还分析了20秒心音数据及其衍生心率参数以供扩展应用(图S18)。图6C流程图展示的空间CNN模型具有四层一维卷积结构(滤波器尺寸逐层递减)，经展平后接入全连接层和Softmax输出层。训练集采用五折交叉验证方案，数据被划分为8000个8秒窗口(采样率250样本/秒)。训练过程中每轮次评估验证准确率，若连续10轮未提升则终止训练。数据来自20名患有各类肺部异常的患者。图6D混淆矩阵显示机器学习对五类样本(四类异常肺音与正常案例对比)的分类准确率达94.78%。当比较CNN与支持向量机在信号分类中的表现时，CNN在2秒和4秒窗口数据分析中均展现出更高准确率(详见图S19)。这种声音异常自动检测技术可便捷集成于移动应用平台供日常活动患者使用(图6E):搭载融合机器学习算法的智能手机应用能实时显示肺音波形并在检测到哮鸣等异常时报警。影片S9演示了该移动应用对模拟多种异常肺音的实时分类过程。

![0196347a-a557-71e6-b3cc-067fd499535a\_8\_282\_252\_1267\_1574\_0.jpg](../../../../../6、知识库/文献知识/4、柔性系统/实验借鉴/国际/团队/Woon-Hong%20Yeo/2022/用于自动化疾病诊断的柔性可穿戴听诊器实现全便携式持续实时听诊/images/0196347a-a557-71e6-b3cc-067fd499535a_8_282_252_1267_1574_0.jpg)

图6. 基于SWS内嵌CNN机器学习的自动化疾病诊断。(A)示意图对比SWS与传统数字听诊器或人工主观听诊的临床数据采集流程。重点展示SWS可提供便携式连续听诊，实时自动诊断多种肺部疾病并反馈给临床医生。(B)爆裂音、干啰音、哮鸣音和喘鸣的样本序列小波尺度图，显示各样本归一化频率与能量密度分布。每个样本被聚类为2秒数据包输入机器学习系统。(C)空间CNN模型流程图:四层一维卷积(滤波器尺寸递减)接展平层，后接全连接层与Softmax输出层。(D)混淆矩阵呈现多位患者四类肺病与正常案例的分类结果，五类总体准确率94.78%。(E)移动应用界面实时显示听诊数据及基于机器学习的异常信号自动检测与分类。(F)受试者佩戴SWS皮肤贴片睡眠的实拍图。(G)模拟打鼾声的10秒频谱图(含舌源性、腭源性等不同模式)，展示频率序列中分解的声信号特征。

软性可穿戴听诊器(SWS)在外形尺寸、便携性和高质量录音方面的独特优势，使其具备应用于睡眠研究的潜力(图6F至G)。佩戴于胸部的柔软设备能成功测量并分离鼾声与心音的不同频段(视频S10)。科学解释表明(26)，鼾声等睡眠呼吸障碍与心血管疾病相关，包括心力衰竭、高血压和心律失常增加(25)。鼾声相对于吸气期的时间分布可揭示其解剖学起源:吸气或呼气时的舌根或软腭振动。与软腭鼾声相比，舌根鼾声在呼吸周期中呈现不规则时序和频谱图上的不一致频段，提示需要筛查治疗的阻塞性睡眠呼吸暂停。此外，鼾声还与喘息、慢性支气管炎等呼吸道症状相关。哮喘合并睡眠呼吸障碍患者表现为更差的睡眠质量和夜间血氧饱和度下降(27)。图6G采集的各类鼾声模拟数据显示${10}\mathrm{\;s}$，包括舌根型与腭型:舌根吸气声在0至${500}\mathrm{\;{Hz}}$Hz呈现独特功率频段及500至$1\mathrm{{kHz}}$Hz的明显峰值，呼气时信号功率递减；而舌根呼气鼾声则显示从吸气状态逐渐增强至${250}\mathrm{\;{Hz}}$Hz的功率特征。该测量同时记录了吸气期软腭鼾声，除350至${400}\mathrm{\;{Hz}}$Hz区间外，其信号功率分布与舌根鼾声相似。本初步研究证实该柔软设备通过同步监测心肺音与电生理信号，有望实现更精准的家庭睡眠监测。

## 讨论

本研究通过软材料工程、降噪机制、柔性力学、信号处理及算法开发的系统性研究，实现了全便携式可穿戴听诊器的连续实时听诊。软性听诊系统在多种日常活动中完成了多被试者的持续心肺监测。计算力学研究为软性可穿戴系统提供了关键设计准则，确保其在反复弯折使用中的机械可靠性。采用生物相容性弹性体与软质粘合剂的系统封装优化方案，实现了亲肤强韧的体表贴合，同时通过应力分布与顺应性层压技术最小化运动伪影。该柔软设备能在被试者不同活动状态下精确检测高质量心肺音。相较于商用数字听诊器，采用小波降噪算法的SWS在四种肺部疾病检测中展现出更优的信噪比性能。与深度学习结合的SWS在临床研究中成功实现多患者连续无线听诊，对啰音、哮鸣音、喘鸣音及干啰音等五类肺部疾病的自动诊断准确率达95%。该设计与系统集成技术与微加工/纳米制造工艺高度兼容，具备较高的技术成熟度。未来研究将开展SWS大规模临床试验，在提供连续数字化实时听诊的同时实现心肺疾病自动诊断，推动数字智能医疗发展。此外，将高灵敏度麦克风的SWS与其他传感模式集成，可拓展至基于个性化生理信号的新一代生物特征安全系统应用。

## 材料与方法

## 设备封装

采用软质弹性体凝胶(Ecoflex, Smooth-On)作为SWS的基础粘附层。凝胶混合物经旋涂形成薄膜后，集成电路被置于凝胶层上方。在织物层(3M9907T)上使用硅胶(High-Tack Silicone Gel, Factor II)，并在封装麦克风岛顶部裁剪圆形织物以优化麦克风压力分布。

## 机械可靠性研究

采用ABAQUS商业软件进行有限元分析以确定设备理想设计。验证阶段使用电动力学测试仪(ESM303, Mark-10)进行循环拉伸弯曲实验。

## 设备性能研究

在扬声器播放20至$2\mathrm{{kHz}}$赫兹信号时，同步测试了SWS与商用设备(TLO)的频率范围。SWS可采集${36}\mathrm{\;{Hz}}$频段的声音，而商用设备受限于${48}\mathrm{\;{Hz}}$；总体而言，SWS展现出更宽广的频率采集范围。

## 声音数据采集

SWS在采集心音时置于Botkin-Erb点(第三肋间隙)(28)，采集肺音时置于右下叶听诊点。每次录音时长为${30}\mathrm{\;s}$，最长可达$2\mathrm{\;{min}}$。受试者需进行不同活动，包括站立(对照活动)、说话、行走和慢跑。

## 数据分析

使用MATLAB分析测量信号。信号处理采用二阶带通滤波，随后应用小波去噪算法。绘制波形图和频谱图时，对输入频谱图函数的窗口进行汉明处理。

## 异常肺音分类

数字化声音数据以4000样本/秒的速率记录，临床医生手动分割并标记数据(正常、湿啰音、干啰音、喘鸣音和哮鸣音)。标记片段被分割为等长的2秒片段(8000个数据点)，连续片段间有75%重叠。使用截止频率为${4.0}\mathrm{\;{Hz}}$的三阶巴特沃斯高通滤波器消除偏移和低频漂移。训练时调整数据集使每类样本数相近。数据输入CNN网络，包含四层一维卷积(滤波器尺寸依次递减为$({32},{24},{16}$和8)和尺寸递增的滤波器($({25},{50},{75}$和100)。每层卷积后接批量归一化和尺寸为2的最大池化。最后经扁平化处理，连接全连接层和Softmax输出。采用Adam优化算法(29,30)调整网络参数，交叉熵损失函数计算误差。通过五折交叉验证方案进行训练以验证性能。

## 信噪比计算

信噪比比较(31)采用功率谱密度估计(Welch法)和汉明窗减少偏差。使用MATLAB提取整个吸气和呼气区域，假设噪声存在于呼吸间歇期。信噪比计算公式如下${\mathrm{{SNR}}}_{dB} = {10}{\log }_{10}\left\lbrack  {\left( \frac{{A}_{\text{signal }}}{{A}_{\text{noise }}}\right) }^{2}\right\rbrack$。

## 心率推导

采用MATLAB的findpeaks函数(32)计算心率。首先使用Pan-Thompkins滤波和小窗口周期性分析排除噪声干扰，随后计算阈值消除S1和S2峰的双重计数，并平滑心率曲线。

## 人体受试研究

研究包含健康与有症状的多组受试者:18至40岁的健康志愿者按佐治亚理工学院批准的机构审查委员会(IRB)方案(#H21038)进行；患者研究遵循忠南大学医院(CNUH)的IRB方案(#2020-10-092)，设备组件交付CNUH研究团队组装成最终人体试验用设备。所有受试者均在研究前签署知情同意书。

## 补充材料

本文补充材料详见https://science.org/doi/10.1126/sciadv.abo5867



![0196347b-092f-7742-b3fe-8d5e5fabe4d3\_1\_235\_228\_1345\_209\_0.jpg](../../../../../6、知识库/文献知识/4、柔性系统/实验借鉴/国际/团队/Woon-Hong%20Yeo/2022/用于自动化疾病诊断的柔性可穿戴听诊器实现全便携式持续实时听诊/images/0196347b-092f-7742-b3fe-8d5e5fabe4d3_1_235_228_1345_209_0.jpg)

图 S1. SWS 设备的二维分层示意图。该设备包含电池供电的印刷电路板，集成的电子元件由 Ecoflex 凝胶、Ecoflex 30和硅树脂层封装，以实现与皮肤贴合接触。

![0196347b-092f-7742-b3fe-8d5e5fabe4d3\_2\_264\_208\_1246\_847\_0.jpg](../../../../../6、知识库/文献知识/4、柔性系统/实验借鉴/国际/团队/Woon-Hong%20Yeo/2022/用于自动化疾病诊断的柔性可穿戴听诊器实现全便携式持续实时听诊/images/0196347b-092f-7742-b3fe-8d5e5fabe4d3_2_264_208_1246_847_0.jpg)

图 S2. 带焊盘的印刷电路板布局。(A) 标注主要集成芯片元件的二维 PCB 布局图。(B) 印刷电路板的三维生成布局图 (俯视图)。(C) 图 B 的仰视图。

![0196347b-092f-7742-b3fe-8d5e5fabe4d3\_3\_270\_247\_1244\_354\_0.jpg](../../../../../6、知识库/文献知识/4、柔性系统/实验借鉴/国际/团队/Woon-Hong%20Yeo/2022/用于自动化疾病诊断的柔性可穿戴听诊器实现全便携式持续实时听诊/images/0196347b-092f-7742-b3fe-8d5e5fabe4d3_3_270_247_1244_354_0.jpg)

图 S3. 设备集成与制造步骤。从裸柔性 PCB 开始，在其表面焊接芯片元件。回流焊后，将集成 PCB 转移至 Ecoflex 凝胶层，顶部放置电池为电路供电。然后用 Ecoflex 30整体封装顶部，最后在麦克风岛区域覆盖硅树脂层。

![0196347b-092f-7742-b3fe-8d5e5fabe4d3\_4\_294\_234\_1201\_610\_0.jpg](../../../../../6、知识库/文献知识/4、柔性系统/实验借鉴/国际/团队/Woon-Hong%20Yeo/2022/用于自动化疾病诊断的柔性可穿戴听诊器实现全便携式持续实时听诊/images/0196347b-092f-7742-b3fe-8d5e5fabe4d3_4_294_234_1201_610_0.jpg)

图 S4. 制造步骤照片。(A) 裸柔性印刷电路板 (PCB)。(B) 顶部焊接芯片元件。(C) 电池集成于芯片元件上方。(D) SWS 设备的最终成品正反面视图。

![0196347b-092f-7742-b3fe-8d5e5fabe4d3\_5\_334\_221\_1127\_722\_0.jpg](../../../../../6、知识库/文献知识/4、柔性系统/实验借鉴/国际/团队/Woon-Hong%20Yeo/2022/用于自动化疾病诊断的柔性可穿戴听诊器实现全便携式持续实时听诊/images/0196347b-092f-7742-b3fe-8d5e5fabe4d3_5_334_221_1127_722_0.jpg)

图 S5. SWS 设备电池使用情况。连接手机的 SWS 设备采用 ${40}\mathrm{{mAh}}$ 电池可持续采集数据达10小时，三次试验均显示相似放电曲线。${150}\mathrm{{mAh}}$ 电池支持最长2天连续记录。

![0196347b-092f-7742-b3fe-8d5e5fabe4d3\_6\_238\_217\_1314\_649\_0.jpg](../../../../../6、知识库/文献知识/4、柔性系统/实验借鉴/国际/团队/Woon-Hong%20Yeo/2022/用于自动化疾病诊断的柔性可穿戴听诊器实现全便携式持续实时听诊/images/0196347b-092f-7742-b3fe-8d5e5fabe4d3_6_238_217_1314_649_0.jpg)

图 S6. SWS 循环机械测试。(A) 两端夹持的 SWS 在 ${20}\%$ 拉伸条件下进行150次循环测试，监测电路电阻变化。(B) SWS 固定于两片玻璃载玻片间，通过 Mark-10测试仪进行150次半径为 ${180}^{ \circ  }$ 毫米的循环弯曲电阻测试。(C) 150次拉伸循环的电阻变化结果。(D) 150次弯曲循环的电阻变化结果。

![0196347b-092f-7742-b3fe-8d5e5fabe4d3\_7\_236\_222\_1298\_456\_0.jpg](../../../../../6、知识库/文献知识/4、柔性系统/实验借鉴/国际/团队/Woon-Hong%20Yeo/2022/用于自动化疾病诊断的柔性可穿戴听诊器实现全便携式持续实时听诊/images/0196347b-092f-7742-b3fe-8d5e5fabe4d3_7_236_222_1298_456_0.jpg)

图 S7. 移动应用程序。(A) SWS 设备连接"麦克风"移动应用，实时传输心脏 S1与 S2信号。(B) 应用自动创建本地"Microphone"文件夹，将实时数据同时存储为. csv 文件和. wav 文件供用户听取心音。

![0196347b-092f-7742-b3fe-8d5e5fabe4d3\_8\_429\_237\_936\_746\_0.jpg](../../../../../6、知识库/文献知识/4、柔性系统/实验借鉴/国际/团队/Woon-Hong%20Yeo/2022/用于自动化疾病诊断的柔性可穿戴听诊器实现全便携式持续实时听诊/images/0196347b-092f-7742-b3fe-8d5e5fabe4d3_8_429_237_936_746_0.jpg)

图 S8. SWS、3M CORE 与3M Littmann 听诊器对比照片。SWS 设备完全摒弃了传统耳件的笨重设计，即使与声学/电子听诊器的胸件部分相比也具有更小的外形尺寸。

![0196347b-092f-7742-b3fe-8d5e5fabe4d3\_9\_299\_206\_1189\_465\_0.jpg](../../../../../6、知识库/文献知识/4、柔性系统/实验借鉴/国际/团队/Woon-Hong%20Yeo/2022/用于自动化疾病诊断的柔性可穿戴听诊器实现全便携式持续实时听诊/images/0196347b-092f-7742-b3fe-8d5e5fabe4d3_9_299_206_1189_465_0.jpg)

图 S9. SWS 硅胶层压力测试。采用 SingleTact 压力传感器对比 Silbione 硅胶层与其他医用胶粘剂对 SWS 设备麦克风岛的压力: (A) 含 Silbione 层的 SWS。(B) 含3M 9907T 胶带的 SWS。(C) 含3M Tegaderm 敷料的 SWS。(D) 含 Micropore 胶带的 SWS。(E) 含3M 2476P 胶带的 SWS。

![0196347b-092f-7742-b3fe-8d5e5fabe4d3\_10\_295\_216\_1191\_1038\_0.jpg](../../../../../6、知识库/文献知识/4、柔性系统/实验借鉴/国际/团队/Woon-Hong%20Yeo/2022/用于自动化疾病诊断的柔性可穿戴听诊器实现全便携式持续实时听诊/images/0196347b-092f-7742-b3fe-8d5e5fabe4d3_10_295_216_1191_1038_0.jpg)

图 S10. SWS 防水测试。(A) SWS 连接移动应用，分别采集设备入水前、水流冲击时及排水后的腕部脉搏信号。(B) 完整防水测试期间的波形图。即使在冲水及排水后，脉搏波形仍能清晰显示 S1和 S2特征峰。

![0196347b-092f-7742-b3fe-8d5e5fabe4d3\_11\_358\_221\_1060\_1159\_0.jpg](../../../../../6、知识库/文献知识/4、柔性系统/实验借鉴/国际/团队/Woon-Hong%20Yeo/2022/用于自动化疾病诊断的柔性可穿戴听诊器实现全便携式持续实时听诊/images/0196347b-092f-7742-b3fe-8d5e5fabe4d3_11_358_221_1060_1159_0.jpg)

图 S11. SWS 胶粘剂透气性测试。(A) 通过装满水的玻璃罐对比测试多种胶粘剂 (开放状态、3M Tegaderm、3M 2476P、Silbione 及 Micropore) 对气体 $\mathrm{H}2\mathrm{O}$ 的渗透性。(B) 室温监测7天后的透气性结果图显示，SWS 胶粘剂透气性优于其他产品。

![0196347b-092f-7742-b3fe-8d5e5fabe4d3\_12\_203\_207\_1384\_1004\_0.jpg](../../../../../6、知识库/文献知识/4、柔性系统/实验借鉴/国际/团队/Woon-Hong%20Yeo/2022/用于自动化疾病诊断的柔性可穿戴听诊器实现全便携式持续实时听诊/images/0196347b-092f-7742-b3fe-8d5e5fabe4d3_12_203_207_1384_1004_0.jpg)

图 S12. SWS 多日连续记录。(A) 配备 ${110}\mathrm{{mAh}}$ 电池的 SWS 设备进行2天心音监测，显示伏案工作与睡眠时信号较纯净，而晨间会议交谈及运动时运动伪影较多。(B) SWS 多日记录粘附力数据。每日8次、每次1分钟的压力传感器读数显示，Silbione 胶层边缘在第3天 ${62}^{\text{nd }}$ 小时后开始分层 (压力值 ${121}\mathrm{\;{Pa}}$)，噪声随之增加。

![0196347b-092f-7742-b3fe-8d5e5fabe4d3\_13\_302\_210\_1161\_784\_0.jpg](../../../../../6、知识库/文献知识/4、柔性系统/实验借鉴/国际/团队/Woon-Hong%20Yeo/2022/用于自动化疾病诊断的柔性可穿戴听诊器实现全便携式持续实时听诊/images/0196347b-092f-7742-b3fe-8d5e5fabe4d3_13_302_210_1161_784_0.jpg)

图 S13. 3天生物相容性与长期佩戴测试。(A) 佩戴 SWS 前的胸部皮肤照片。(B) 连续佩戴3天后的皮肤状态。设备贴附区域可见轻微泛红，但未发生刺激反应。

![0196347b-092f-7742-b3fe-8d5e5fabe4d3\_14\_336\_208\_1104\_502\_0.jpg](../../../../../6、知识库/文献知识/4、柔性系统/实验借鉴/国际/团队/Woon-Hong%20Yeo/2022/用于自动化疾病诊断的柔性可穿戴听诊器实现全便携式持续实时听诊/images/0196347b-092f-7742-b3fe-8d5e5fabe4d3_14_336_208_1104_502_0.jpg)

图 S14. SWS 单次快速傅里叶变换信号。采集的声信号被分解为独立频谱成分，分别显示 (A) 心音与 (B) 肺音的频率谱图及对应功率振幅。

![0196347b-092f-7742-b3fe-8d5e5fabe4d3\_15\_296\_210\_1200\_775\_0.jpg](../../../../../6、知识库/文献知识/4、柔性系统/实验借鉴/国际/团队/Woon-Hong%20Yeo/2022/用于自动化疾病诊断的柔性可穿戴听诊器实现全便携式持续实时听诊/images/0196347b-092f-7742-b3fe-8d5e5fabe4d3_15_296_210_1200_775_0.jpg)

图 S15. 频率范围测试对比 ThinkLabs One。播放 ${20}\mathrm{{Hz}}$ 至 ${750}\mathrm{{Hz}}$ 的线性调频音以比较与商用数字听诊器 TLO 的频率范围性能。(A) 该图显示 TLO 从 ${48}\mathrm{\;{Hz}}$ 起始的基准频率。(B) 该图显示 SWS 从 ${36}\mathrm{\;{Hz}}$ 起始的基准频率。

![0196347b-092f-7742-b3fe-8d5e5fabe4d3\_16\_281\_211\_1239\_983\_0.jpg](../../../../../6、知识库/文献知识/4、柔性系统/实验借鉴/国际/团队/Woon-Hong%20Yeo/2022/用于自动化疾病诊断的柔性可穿戴听诊器实现全便携式持续实时听诊/images/0196347b-092f-7742-b3fe-8d5e5fabe4d3_16_281_211_1239_983_0.jpg)

图 S16. 小波去噪算法及去噪心肺信号。(A) 小波去噪算法步骤: 通过高低通滤波器组和下采样将原始信号分解为多级近似系数与细节系数，经上采样后通过高低通滤波器组合成去噪信号。(B) 小波去噪肺部信号清晰显示吸呼气过程，局部放大图展示信号各小波分量。(C) 小波去噪心音信号含 S1、S2峰。(D) S1与 S2峰间各小波分量反映局部噪声。

![0196347b-092f-7742-b3fe-8d5e5fabe4d3\_17\_346\_208\_1108\_963\_0.jpg](../../../../../6、知识库/文献知识/4、柔性系统/实验借鉴/国际/团队/Woon-Hong%20Yeo/2022/用于自动化疾病诊断的柔性可穿戴听诊器实现全便携式持续实时听诊/images/0196347b-092f-7742-b3fe-8d5e5fabe4d3_17_346_208_1108_963_0.jpg)

图 S17. 3M CORE 与 SWS 的频谱图对比。(A) 3M Littmann CORE 数字听诊器与背面 SWS 的实物图。(B) 3M CORE 吸呼气肺音的时序频谱图。(C) SWS 设备吸呼气声频谱图显示较 $3\mathrm{M}$ Littmann CORE 在 $\mathrm{B}$ 部分信号更清晰。

![0196347b-092f-7742-b3fe-8d5e5fabe4d3\_18\_295\_220\_1169\_1436\_0.jpg](../../../../../6、知识库/文献知识/4、柔性系统/实验借鉴/国际/团队/Woon-Hong%20Yeo/2022/用于自动化疾病诊断的柔性可穿戴听诊器实现全便携式持续实时听诊/images/0196347b-092f-7742-b3fe-8d5e5fabe4d3_18_295_220_1169_1436_0.jpg)

图 S18. 心肺信号小波分析与心率推导。(A) 三维尺度图: 将小波变换信号转换为时频图。(B) 心音二维尺度图。(C) 肺音二维尺度图。所有尺度图采用 jet 色标，暖色表示特定频段强小波。(D) 心电信号峰值检测。(E) 通过 $\mathrm{D}$ 部分峰间时差计算得出的时序心率图。

![0196347b-092f-7742-b3fe-8d5e5fabe4d3\_19\_532\_208\_737\_612\_0.jpg](../../../../../6、知识库/文献知识/4、柔性系统/实验借鉴/国际/团队/Woon-Hong%20Yeo/2022/用于自动化疾病诊断的柔性可穿戴听诊器实现全便携式持续实时听诊/images/0196347b-092f-7742-b3fe-8d5e5fabe4d3_19_532_208_737_612_0.jpg)

图 S19. CNN 在机器学习中的显著性。CNN 方法与支持向量机 (SVM) 在信号分类中的对比显示: CNN 对2秒和4秒窗口数据分析平均准确率达93%，而 SVM FFT 方法平均为66%。

表 S1. SWS 与3M CORE 的全部临床试验数据对比。

<table><tr><td colspan="2"/><td>3M Littmann CORE</td><td>Wearabl e Stethosc ope</td><td colspan="2"/><td>3M Littmann CORE</td><td>Wearable Stethoscop e</td></tr><tr><td rowspan="3">Patient A (Male, 64)</td><td>SNR [dB]</td><td>9.26</td><td>10.72</td><td rowspan="3">Patient K (Male, 73)</td><td>SNR [dB]</td><td>4.29</td><td>5.27</td></tr><tr><td>Patient Number</td><td colspan="2">1</td><td>Patient Number</td><td colspan="2">11</td></tr><tr><td>Sympto m</td><td colspan="2">Crackle</td><td>Symptom</td><td colspan="2">Crackle</td></tr><tr><td rowspan="3">Patient B (Female, 65)</td><td>SNR [dB]</td><td>4.77</td><td>8.44</td><td rowspan="3">Patient L (Male, 68)</td><td/><td>3.93</td><td>8.78</td></tr><tr><td>Patient Number</td><td colspan="2">2</td><td>Patient Number</td><td colspan="2">12</td></tr><tr><td>Sympto m</td><td colspan="2">Crackle</td><td>Symptom</td><td colspan="2">Crackle</td></tr><tr><td rowspan="3">Patient C (Male, 83)</td><td>SNR [dB]</td><td>5.63</td><td>4.04</td><td rowspan="3">Patient M (Male, 66)</td><td>SNR [dB]</td><td>11.62</td><td>26.23</td></tr><tr><td>Patient Number</td><td colspan="2">3</td><td>Patient Number</td><td colspan="2">13</td></tr><tr><td>Sympto m</td><td colspan="2">Crackle</td><td>Symptom</td><td colspan="2">Rhonchi</td></tr><tr><td rowspan="3">Patient D (Female, 64)</td><td>SNR [dB]</td><td>6.77</td><td>12.11</td><td rowspan="3">Patient $\mathrm{N}$ (Female, 55)</td><td>SNR [dB]</td><td>9.59</td><td>11.42</td></tr><tr><td>Patient Number</td><td colspan="2">4</td><td>Patient Number</td><td colspan="2">14</td></tr><tr><td>Sympto m</td><td colspan="2">Crackle</td><td>Symptom</td><td colspan="2">Wheeze</td></tr><tr><td rowspan="3">Patient E (Male, 75)</td><td>SNR [dB]</td><td>9.95</td><td>10.29</td><td rowspan="3">Patient O (Male, 76)</td><td>SNR [dB]</td><td>6.65</td><td>13.75</td></tr><tr><td>Patient Number</td><td colspan="2">5</td><td>Patient Number</td><td colspan="2">15</td></tr><tr><td>Sympto m</td><td colspan="2">Crackle</td><td>Symptom</td><td colspan="2">Wheeze</td></tr></table>

<table><tr><td rowspan="3">Patient F (Male, 68)</td><td>SNR [dB]</td><td>7.86</td><td>11.17</td><td rowspan="3">Patient P (Male, 72)</td><td>SNR [dB]</td><td>15.29</td><td>16.75</td></tr><tr><td>Patient Number</td><td colspan="2">6</td><td>Patient Number</td><td colspan="2">16</td></tr><tr><td>Sympto m</td><td colspan="2">Crackle</td><td>Symptom</td><td colspan="2">Wheeze</td></tr><tr><td rowspan="3">Patient G (Male, 56)</td><td>SNR [dB]</td><td>9.1</td><td>17.37</td><td rowspan="3">Patient Q (Female, 72)</td><td>SNR [dB]</td><td>3.84</td><td>10.33</td></tr><tr><td>Patient Number</td><td colspan="2">7</td><td>Patient Number</td><td colspan="2">17</td></tr><tr><td>Sympto m</td><td colspan="2">Crackle</td><td>Symptom</td><td colspan="2">Wheeze</td></tr><tr><td rowspan="3">Patient H (Male, 66)</td><td>SNR [dB]</td><td>15.39</td><td>23.84</td><td rowspan="3">Patient R (Male, 60)</td><td>SNR [dB]</td><td>11.96</td><td>12.93</td></tr><tr><td>Patient Number</td><td colspan="2">8</td><td>Patient Number</td><td colspan="2">18</td></tr><tr><td>Sympto m</td><td colspan="2">Crackle</td><td>Symptom</td><td colspan="2">Stridor</td></tr><tr><td rowspan="3">Patient I (Female, 72)</td><td>SNR [dB]</td><td>9.89</td><td>16.24</td><td rowspan="3">Patient S (Male, 84)</td><td>SNR [dB]</td><td>11.39</td><td>23.55</td></tr><tr><td>Patient Number</td><td colspan="2">9</td><td>Patient Number</td><td colspan="2">19</td></tr><tr><td>Sympto m</td><td colspan="2">Crackle</td><td>Symptom</td><td colspan="2">Stridor</td></tr><tr><td rowspan="3">Patient J (Male, 77)</td><td>SNR [dB]</td><td>8.02</td><td>9.06</td><td rowspan="3">Patient T (Male, 61)</td><td>SNR [dB]</td><td>15.39</td><td>23.84</td></tr><tr><td>Patient Number</td><td colspan="2">10</td><td>Patient Number</td><td colspan="2">20</td></tr><tr><td>Sympto m</td><td colspan="2">Crackle</td><td>Symptom</td><td colspan="2">Stridor</td></tr></table>

补充视频1. SWS 受试者运动伪影控制。

补充视频2. SWS 与 TLO 在行走和跑动中的运动伪影噪声对比。

补充视频3. SWS 采集的正常心音。

补充视频4. SWS 采集的正常肺音。

补充视频5. 开源细湿啰音样本。

补充视频6. 开源干啰音样本。

补充视频7. 开源喘鸣音样本。

补充视频8. 开源哮鸣音样本。

补充视频9. 多种肺部症状分类的机器学习演示。

补充视频10. 睡眠应用中受试者吸呼气时的腭鼾声记录。


基于云计算实现实时连续运动识别的全身穿戴式纺织集成纳米材料与柔性电子系统
Full Body-Worn Textile-Integrated Nanomaterials and Soft Electronics for Real-Time Continuous Motion Recognition Using Cloud Computing
权康奎，李允宰，郑秀英，李智敏，罗艺媛，权永进，申范俊，艾莉森·贝特曼，李在浩，马修·盖斯，孙正宇*，李振宇*，梁元洪* 引用本文:ACS应用材料与界面 2025, 17, 7977-7988

访问全文 指标与更多

摘要:人体运动识别为实时监测用户日常活动开辟了新途径，将彻底变革持续健康监护与康复领域。尽管部分可穿戴传感器已展现运动检测能力，但此前研究均未能实现无线设备的全身动作捕捉。本文提出一种集成纳米材料与柔性传感器的软电子纺织系统，通过无线传感服与深度学习云计算相结合，实现多种全身运动的实时检测。该系统包含纳米薄膜阵列、激光诱导石墨烯应变传感器及纺织集成柔性电子元件，可无线检测不同身体运动与锻炼行为。经多人测试表明，该系统能实时预测静息、行走、跑步、深蹲、上楼、下楼、俯卧撑和跳绳八类活动，准确率达95.3%。该技术体系作为全身穿戴式纺织电子设备，通过与智能手表和便携设备交互配对，可应用于现实场景如动态健康监测及具备反馈功能的定制化康复训练。

![019681ad-000d-71a7-b6f9-3bb50c4eaed0\_0\_914\_831\_708\_351\_0.jpg](images/019681ad-000d-71a7-b6f9-3bb50c4eaed0_0_914_831_708_351_0.jpg)

关键词:可穿戴电子，纺织集成传感器，云计算，运动识别，深度学习

## 1. 引言

材料科学、制造技术与人工智能的融合发展，正推动可穿戴生物电子在人类健康医疗领域的应用。实时预测人体运动意图为医疗康复行业创造新机遇，因其能即时获取用户日常活动的关键数据。尽管最新研发的可穿戴应变传感器已实现高灵敏度人体运动检测，${}^{1 - 5}$但材料设计与集成的局限阻碍了其在健康监护技术中的实际应用。现有应变传感器普遍缺乏便携式电路支持应变数据的持续无线采集，${}^{1,2,5 - {11}}$导致无法实时监测人体运动。除连续运动检测外，多数研究因缺少实时多类运动分类的机器学习模型，${}^{{12} - {19}}$难以预测目标动作。部分研究虽能基于手部${}^{4,{20},{21}}$或喉部${}^{{22},{23}}$等局部应变信息分类人体动作，但将监测范围扩展至全身将为健康监护带来全新视角。全身运动预测通过革新医疗康复${}^{24}$、运动生物力学${}^{{25},{26}}$和人机交互${}^{27}$等领域，${}^{28}$实现用户日常活动的持续评估。柔性电子与纺织集成设计等材料工程的突破，为全身运动识别的可穿戴系统奠定基础。然而现有纺织集成应变传感器仍无法实现便携式无线全身运动检测及基于机器学习的实时动作识别。${}^{{29} - {31}}$总体而言，先前研究缺乏对可穿戴传感服实际应用所需功能模块的系统整合。

![019681ad-000d-71a7-b6f9-3bb50c4eaed0\_0\_1490\_1962\_130\_185\_0.jpg](images/019681ad-000d-71a7-b6f9-3bb50c4eaed0_0_1490_1962_130_185_0.jpg)

---

收稿日期:2024年10月9日

修回日期:2025年1月12日

录用日期:2025年1月14日

发布日期:2025年1月24日

---

![019681ad-000d-71a7-b6f9-3bb50c4eaed0\_1\_231\_174\_1304\_1111\_0.jpg](images/019681ad-000d-71a7-b6f9-3bb50c4eaed0_1_231_174_1304_1111_0.jpg)

图1. 基于云计算的实时连续运动识别全身纺织集成柔性微电子系统概览。A.多路应变传感服示意图。B.智能手表与传感服配对时的图形用户界面模拟。C.从身体运动、应变传感、数据预处理到机器学习最终输出至便携设备界面的完整运动预测流程图。

本研究介绍了一种基于云端机器学习的全身纺织集成电子系统，用于无线实时运动识别。该电子系统由多路复用的纳米膜石墨烯传感器和柔性电路组成，可采集人体应变信息并通过无线传输至云端进行连续运动识别。通过将魔术贴网格纺织材料集成到柔性电子器件中，该系统能符合人体工学地附着于运动预测服上，即使不同体型的使用者也能轻松调节。此外，云端深度学习技术可实时处理多路复用的人体应变数据，预测用户即将进行的运动类型。该可穿戴传感服能实时识别八类预设运动，为增强实际应用性，我们通过蓝牙将其与智能手表等便携设备配对，实现个性化运动反馈及卡路里计算。鉴于这种具有实用功能的新型全身纺织电子器件尚未见报道，我们预期该纺织传感电子技术将推动医疗健康应用和康复科学领域的变革。

## 2. 结果与讨论

2.1. 基于云计算的全身纺织集成柔性电子系统概述:用于无线实时连续运动识别。图1A展示了多路复用激光诱导石墨烯(LIG)应变服及其内置纺织集成应变传感器单元的示意图。该应变服包含八个无线柔性应变传感器和一个惯性测量单元(IMU)传感器，分别附着于三角肌、肘部、臀肌、膝盖和背部以获取人体动态信息。位于背部的IMU传感器建立了三维空间参考点，可持续监测用户空间方位。无线应变传感器主要由柔性电路、LIG传感单元、魔术贴片和互联电极构成。柔性聚酰亚胺(PI)电路实时采集人体应变数据并无线传输至云端系统，经上传的深度学习模型对运动类型进行分类。由$\mathrm{{PI}},\mathrm{{Cu}}$和$\mathrm{{PI}}$三层材料构成的蛇形互联电极连接无线电路与传感单元。传感器单元两端的魔术贴片便于快速穿戴，使其适配不同体型使用者。如图1B所示，与智能手表集成后，设备无需用户手动选择运动类型，深度学习模型可自动识别当前运动，用户可无缝开展锻炼同时获得智能手表自动计算的卡路里消耗数据。图$1\mathrm{C}$展示了从各身体部位应变数据预测用户运动意图的算法流程图:当用户穿着传感服进行特定锻炼时，六个身体关节会产生不同的应变信号组合，多路复用应变数据库信息经无线传输至云端平台后，通过包含信号分割与校正的预处理转化为机器学习适用输入，经时间戳校准的八通道应变信号用于训练机器学习模型。云端机器学习的重要性在于能访问海量应变数据库进行迭代训练，从而提升预测精度。云平台允许开发者随时随地进行模型更新，最终通过与智能手表集成，用户无需手动操作设备界面即可获取实时运动类型反馈。

![019681ad-000d-71a7-b6f9-3bb50c4eaed0\_2\_342\_168\_1078\_882\_0.jpg](images/019681ad-000d-71a7-b6f9-3bb50c4eaed0_2_342_168_1078_882_0.jpg)

图2. 用于应变传感的LIG电极表征。A.通过激光参数化研究获取最大灵敏系数的激光辐照条件。B.带有镀铜接触垫的LIG电极实物照片，顶部三图分别为使用中LIG电极的光学显微图像、表面轮廓图像和扫描电镜图像，右下角图像显示带镀铜垫的LIG传感器高度随长度位置变化情况。C.LIG电极的拉曼光谱。D.10%应变下2000次循环测试的归一化电阻变化，插图为循环测试中段的应变周期放大图。E.LIG传感器在1%、2%、5%、10%至20%应变梯度下的逐步归一化电阻变化。F.LIG传感器在$2\% ,4\% ,6\% ,8\%$和10%应变下呈现升降趋势的逐步归一化电阻变化。G.4%应变下LIG传感器的响应与恢复特性。

2.2 应变传感器用LIG电极表征。为获得高灵敏系数(GF)，我们通过调节主要影响激光能量密度的扫描速度和强度参数进行参数化研究(图2A)。GF测量采用标准化流程:将LIG应变传感器安装在电动拉伸平台上，在受控环境中施加渐进应变(如5%、10%、20%)，使用LCR表持续记录拉伸-释放循环中的电阻变化，并按公式计算GF:

$$
\mathrm{{GF}} = {\Delta R}/\left( {{R}_{\mathrm{o}} \times  \varepsilon }\right)
$$

式中${\Delta R}$为应变引起的电阻变化量，${R}_{0}$为传感器初始电阻，$\varepsilon$为施加应变值

![019681ad-000d-71a7-b6f9-3bb50c4eaed0\_3\_320\_167\_1135\_1156\_0.jpg](images/019681ad-000d-71a7-b6f9-3bb50c4eaed0_3_320_167_1135_1156_0.jpg)

图3. LIG应变传感器与功能组件集成示意图。A.完整LIG应变传感器的顶视、底视与侧视图，包含LIG电极、魔术贴网布、互连件和柔性电路，插图为传感单元(左)与电路单元(右)的爆炸视图。B.集成应变传感器制造流程图示。C.标注集成电路(IC)元件的柔性电路顶视图。D.包含应变传感器、便携设备与云计算的系统流程图。

为验证可重复性，对相同条件下制备的多个传感器进行重复测试。所有参数研究样本的GF测量结果表明，${125}\mathrm{\;{mm}}{\mathrm{\;s}}^{-1}$和${7.5}\mathrm{\;W}$参数组合可产生${96.35} \pm  {4.62}$的最大GF值。人体运动通常涉及0%至${20}\%$的应变范围，如关节屈曲、肌肉收缩和姿势调整。高GF值能精准检测这些应变变化，有效区分细微与大幅度动作，这种灵敏度对于解析行走、奔跑、深蹲等不同活动产生的时空应变特征至关重要。${}^{5}$激光扫描速度与强度是决定激光诱导石墨烯(LIG)制备过程中能量密度的关键参数，直接影响石墨烯层的结构形态特征，进而调控应变传感器的GF值。高激光能量密度通常产生高GF值，因其形成的多孔纳米-微米级结构能促进(1)应变下石墨烯网络的可逆接触变化，(2)多样化的跃迁/隧穿效应。${}^{{32},{33}}$但过高的激光能量会烧蚀PI基底，且破坏层级结构导致致密化，反而会限制实现高GF所必需的隧穿效应和可逆接触变化。因此存在最优激光加工参数可使石墨烯电极获得${96.35} \pm  {4.62}$的高GF值。

图2B展示了从聚酰亚胺(PI)薄膜转移至聚二甲基硅氧烷(PDMS)层的优化激光诱导石墨烯(LIG)传感器电极实物图。该传感器电极采用传统单轴应变片构型，总长度为${25}\mathrm{\;{mm}}$(如插图所示)。插图中的光学与扫描电镜图像呈现LIG的多尺度形貌，其分级多孔微结构可增强灵敏度(如前述讨论)。彩色表面轮廓显示LIG电极高度约为${59.6} \pm  {5.5\mu }\mathrm{m}$(图S1)，粉色阴影区域代表电镀的$\mathrm{{Cu}}$层，用于实现与传统互连件的焊接兼容。图S2进一步展示了PDMS基底上转移LIG电极的俯视与截面视图，其中非阴影区对应LIG电极。图S2B的能谱(EDX)面扫分析证实了电极、PDMS及电镀铜的化学成分。图2C中拉曼光谱为判断PI基底激光辐照后石墨烯的存在提供了关键依据，其特征峰包括$\mathrm{D}$带(约${1340}{\mathrm{\;{cm}}}^{-1}$)、$\mathrm{G}$带(约1585${\mathrm{{cm}}}^{-1}$)和$2\mathrm{D}$带(约${2696}{\mathrm{\;{cm}}}^{-1}$)。通常拉曼分析中，$\mathrm{D}$带反映${\mathrm{{sp}}}^{2}$杂化$\mathrm{C}$键或缺陷，$\mathrm{G}$带对应一级声子，$2\mathrm{D}$带指示二级边界声子。${}^{34}$0.842的${\mathrm{I}}_{2\mathrm{D}}/{\mathrm{I}}_{\mathrm{G}}$比值表明形成了单层石墨烯(该值远低于2)。图S3提供了不同激光功率与扫描速度参数下的拉曼光谱曲线，证实所有参数条件下D带位移可忽略且均存在单层石墨烯。${}^{35}$为测试机械耐久性与应变传感重复性，我们对传感器电极施加10%循环应变2000次(图2D，插图为放大拉伸循环)。全程稳定的归一化电阻证实了重复使用后的机械鲁棒性与数据可靠性。图$2\mathrm{E}$展示了LIG传感器在50次应变循环中逐步变化的归一化电阻，应变水平从$1\% ,2\% ,5\% ,{10}\%$递增至${20}\%$。图$2\mathrm{\;F}$详细记录了电阻随应变阶梯式升降的变化规律(具体以$2\%$为间隔，经$4\% ,6\%$、$8\%$最终达${10}\%$)，表明归一化电阻与应变量呈正相关，凸显传感器通过电阻变化可靠区分不同应变量的能力。此外，当传感器承受10%应变后恢复初始状态时，测得80ms与${225}\mathrm{\;{ms}}$的快速响应/恢复时间(图2G)。这种即时响应特性对本研究至关重要，因为传感器需实时监测人体应变信息，响应延迟可能显著影响机器学习分类的连续性精度。

2.3. 基于激光诱导石墨烯应变传感器与柔性电路的软体集成电子器件制备。图3A展示了软体LIG应变传感器单元的集成设计，主要由LIG电极、魔术贴网布、蛇形互连线和柔性电路组成。魔术贴网布便于与服装快速贴合，而织物集成设计支持轻松拆卸调节位置。符合人体工学的传感器可独立于服装物理分离，完美适配不同体型用户。蛇形互连线在LIG电极与柔性电路间建立电气连接，有效抑制运动伪影噪声。柔性电路集成定制化板载电池，通过磁吸线充电实现完全无线化设计，消除供电线缆张力噪声。图3B详述LIG应变传感器的分步制备流程:首先通过紫外激光热解聚酰亚胺基底生成石墨烯，并在LIG电极接触垫电镀铜实现蛇形互连；随后用PDMS包埋LIG电极便于转印至PDMS基底，并以PDMS薄膜封装另一侧作为保护层；最后整合魔术贴网布与柔性电路完成制备。图3C所示应变传感器的柔性电路包含连续数据记录与无线传输核心模块:电阻测量专用集成电路(ADS 1292)配合惠斯通电桥阵列实现微阻精准测量，微处理器单元(MPU)与蓝牙天线无线传输LIG电极采集的实时电阻数据。如图S4所示，集成惯性测量单元(ICM 20948)监测三维空间姿态，其MPU模块同样支持无线数据传输与实时采集。图3D呈现LIG应变传感器与数字技术的无缝集成，实现全方位运动追踪与增强用户反馈。系统采用Google Flutter框架开发的移动应用，兼容iOS/Android双平台，承担可穿戴设备数据的实时采集、本地存储与云端同步。云计算基础设施集成流式/批处理数据引擎，结合基于FastAPI的运动预测算法应用引擎，构建高效数据处理架构。可穿戴技术与云计算的协同实现了实时健康监测与高级分析，确立该系统的远程健康监测前沿地位。图4A展示受试者穿着多通道应变传感服，传感器分布于三角肌、膝盖、肘部和臀肌以获取关节应变信息。当受试者进行肩外展(图4B)、肘屈曲(图4C)、膝屈曲(图4D)及深蹲(图4E)时，各部位传感器的归一化电阻快速响应。肘部与三角肌传感器分别可靠测量高达${145}^{ \circ  }$和${180}^{ \circ  }$的运动范围，膝部与臀肌传感器分别覆盖${140}^{ \circ  }$和${125}^{ \circ  }$。该能力支持全身大尺度与细微应变变化的综合追踪，实时电阻响应表明结合机器学习可基于多通道应变模式识别用户运动意图。

2.4. 实时连续运动监测的全织物集成电子系统演示

![019681ad-000d-71a7-b6f9-3bb50c4eaed0\_5\_140\_167\_713\_932\_0.jpg](images/019681ad-000d-71a7-b6f9-3bb50c4eaed0_5_140_167_713_932_0.jpg)

图4. 关节运动监测用多通道应变传感服。A.穿着应变传感服的受试者正/背/侧视图，特写显示三角肌、膝盖、肘部和臀肌的应变传感器。B-E.通过三角肌(B)、肘部(C)、膝盖(D)和臀肌(E)的归一化电阻变化监测关节运动。

基于云计算的动作识别技术研究。本研究通过融合卷积神经网络(CNN)与门控循环单元(GRU)的混合深度学习模型，开发了一套日常活动监测方法。该方案结合了CNN的空间特征提取能力和GRU(长短期记忆网络LSTM的变体)的时序数据处理优势，在实时场景中实现了更高精度的动态评估。${}^{{36},{37}}$图$5\mathrm{\;A}$展示了从数据采集到最终预测阶段的实时动作识别流程图。我们采集了五名健康受试者的全身运动数据用于模型训练。受试者穿着多路应变服装完成七种指定动作(如图5B所示)，该图同时捕捉了每个动作的起始与结束姿态，直观呈现目标运动特征。通过多传感器时空关联分析，可有效区分深蹲与弓步等相似动作——虽然都涉及膝关节弯曲，但其时间激活序列与其他身体部位的代偿运动存在差异。此外，惯性测量单元(IMU)提供的空间方位数据能为应变信号提供上下文参照，增强动作区分度。经过预处理后，深度学习模型处理$5\mathrm{\;s}$长时滤波的应变与IMU数据。图5C展示了用于全身运动分析的CNN-GRU混合模型架构，其首先将应变和IMU信号归一化至0-1区间，随后按6:2:2比例划分训练集、验证集和测试集。模型采用带参数修正线性单元(PReLU)作为激活函数，配合ADAM优化器，通过分类交叉熵函数计算损失。超参数采用随机选择法微调，并设置连续两次评估无改进时提前终止训练。网络结构包含两个批归一化(BN)和PReLU激活的一维卷积层，一个GRU层，以及两个带dropout的全连接层防止过拟合。最终通过softmax激活层将数据分类为静止、行走、跑步、深蹲、上楼、下楼、俯卧撑、跳绳八种状态(如图5D应变信号和图S5 IMU信号所示)。模型在混淆矩阵(图SE)中显示出95.27%的高准确率，电影S1演示了每十秒从静止状态切换到不同训练动作的实时预测过程。图S6展示了云端图形用户界面的截图，该界面用于实时运动分析与反馈。纺织电子器件可与智能手表等便携设备协同工作，预测用户运动意图并实时计算卡路里消耗。虽然智能手表能基于IMU数据估算特定运动的能耗${}^{{38},{39}}$，但单通道IMU信息无法准确识别正在进行的具体运动类型。本研究的可穿戴传感器接口则能基于模型实时分类结果(图SF和电影S2)，自动生成卡路里计算与活动追踪报告。系统采用

卡路里$= \mathrm{{MET}} \times  {3.5} \times  \left( {\text{Weight in}\mathrm{{kg}}}\right)  \times  \left( \text{Time in minutes}\right) /{200}$

其中MET值(代谢当量)针对每项活动进行校准(静息:2.9，步行:3.9，跑步:7.4，爬楼梯:5.9，深蹲:10.4)。对于${70}\mathrm{\;{kg}}$个体，这转化为实际的能量消耗，例如步行(${240}\mathrm{{kcal}}/{50}\mathrm{\;{min}}$)和跑步(${270}\mathrm{{kcal}}/{30}\mathrm{\;{min}}$)。校准后的MET值会根据实时运动分类自动应用，从而实现整个锻炼过程中精确的卡路里追踪。因此，这套可穿戴传感服能提供基于动作激活的个性化反馈，并促进长期锻炼效果。本研究的演示凸显了该系统作为日常活动实时观察的有效工具能力，在健康监测与康复科学领域取得显著进展。如表1所示，我们的系统在无线连接、多动作识别和云端集成方面的能力超越现有技术，使其成为实时健康监测和个性化康复的独特解决方案。

![019681ad-000d-71a7-b6f9-3bb50c4eaed0\_6\_365\_174\_1030\_1408\_0.jpg](images/019681ad-000d-71a7-b6f9-3bb50c4eaed0_6_365_174_1030_1408_0.jpg)

图5. 展示基于云计算实现实时连续动作识别的全身纺织集成电子系统。A. 从训练数据集到数据预处理、机器学习及结果预测的完整动作识别流程图。B. 穿着集成电子元件的全身服进行步行、跑步、深蹲、俯卧撑、攀爬和跳绳等不同动作的受试者照片。C. 实时预测用户目标动作的深度学习架构流程图。D. 根据受试者不同活动(包括静息、步行、跑步、深蹲、上楼、下楼、俯卧撑和跳绳)测量的身体各部位传感器电阻变化。E. 预测用户目标动作的混淆矩阵，显示对D中8类动作的分类准确率达95.3%。F. 与平板电脑和智能手表的交互配对，展示个性化锻炼报告。

## 3. 结论

本文介绍了一种基于云端机器学习、通过应变信息识别用户实时锻炼动作的多路复用柔性可穿戴应变传感服。全身纺织物上制备的LIG电极展现出高灵敏系数和快速响应/恢复特性，适用于连续实时动作预测应用。与传统应变传感器不同，本研究的纺织电子器件集成了无线柔性电路与阵列式膜应变传感器，采用柔性网状贴片设计。这些以材料为核心的进步保障了用户舒适度，减少运动伪影，并通过无线信号检测实现复杂动作识别。基于云端的机器学习对八类用户锻炼动作的识别准确率达95.27%。结合智能手表集成，该可穿戴传感服能自动选择锻炼类型。每次锻炼结束后，系统会生成详细记录活动成果和能量消耗的综合报告。这项研究突显了材料工程、系统集成和云计算方面的进步，将为开发各类柔性智能电子设备以推动智能锻炼和智能医疗提供重要启示。

表1. 全身穿戴式电子设备与现有技术在人體動作檢測方面的性能对比

<table><tbody><tr><td></td><td>全身运动检测一体化无线应变传感器</td><td>云计算</td><td>与智能手表/便携设备交互配对</td><td>可预测用户意图动作</td><td>直接织物集成</td><td>传感器数量</td><td>动作数量</td></tr><tr><td>本研究</td><td>O</td><td>O</td><td>O</td><td>O</td><td>O</td><td>9</td><td>8</td></tr><tr><td>40</td><td>X</td><td>$X$</td><td>$X$</td><td>X</td><td>X</td><td>5</td><td>6</td></tr><tr><td>41</td><td>$X$</td><td>$X$</td><td>$X$</td><td>$X$</td><td>$X$</td><td>1</td><td>2</td></tr><tr><td>42</td><td>$X$</td><td>X</td><td>X</td><td>$X$</td><td>$X$</td><td>1</td><td>2</td></tr><tr><td>43</td><td>$X$</td><td>X</td><td>X</td><td>$X$</td><td>X</td><td>5</td><td>10</td></tr><tr><td>44</td><td>$X$</td><td>$X$</td><td>$X$</td><td>X</td><td>$X$</td><td>1</td><td>2</td></tr><tr><td>45</td><td>$X$</td><td>$X$</td><td>$X$</td><td>X</td><td>$X$</td><td>1</td><td>2</td></tr><tr><td>5</td><td>$X$</td><td>$X$</td><td>$X$</td><td>X</td><td>$X$</td><td>18</td><td>3</td></tr><tr><td>46</td><td>$X$</td><td>$X$</td><td>$X$</td><td>X</td><td>$X$</td><td>1</td><td>4</td></tr><tr><td>47</td><td>$X$</td><td>$X$</td><td>$X$</td><td>X</td><td>O</td><td>1</td><td>2</td></tr><tr><td>48</td><td>$X$</td><td>X</td><td>X</td><td>X</td><td>O</td><td>1</td><td>3</td></tr><tr><td>49</td><td>X</td><td>$X$</td><td>$X$</td><td>X</td><td>O</td><td>2</td><td>4</td></tr><tr><td>21</td><td>X</td><td>$X$</td><td>$X$</td><td>O</td><td>X</td><td>2</td><td>9</td></tr><tr><td>50</td><td>$X$</td><td>$X$</td><td>$X$</td><td>O</td><td>X</td><td>3</td><td>7</td></tr><tr><td>51</td><td>$X$</td><td>$X$</td><td>X</td><td>O</td><td>X</td><td>1</td><td>13</td></tr><tr><td>4</td><td>$X$</td><td>$X$</td><td>$X$</td><td>O</td><td>O</td><td>4</td><td>6</td></tr><tr><td>52</td><td>$X$</td><td>$X$</td><td>$X$</td><td>O</td><td>X</td><td>2</td><td>6</td></tr><tr><td>53</td><td>X</td><td>X</td><td>X</td><td>X</td><td>X</td><td>46</td><td>10</td></tr><tr><td>54</td><td>$X$</td><td>X</td><td>$X$</td><td>X</td><td>$X$</td><td>5</td><td>5</td></tr></tbody></table>

## 四、实验部分

4.1 LIG电极制备。将PI胶带粘贴于载玻片，随后使用紫外激光器(阿拉巴马激光器)生成LIG，如图S7所示。通过电源(Keithley 2200直流电源)施加${0.06}\mathrm{\;A}$电流持续$5\mathrm{\;{min}}$进行电镀。图S8展示了电镀$\mathrm{{Cu}}$以促进互连焊点形成的方法。在LIG电极末端制作铜垫，用于焊接互连器。将PDMS(道康宁184硅橡胶)以${100}\mathrm{{rpm}}$转速旋涂，并于${100}{}^{ \circ  }\mathrm{C}$固化${20}\mathrm{\;{min}}$。将LIG电极转移至PDMS后，以${50}\mathrm{{rpm}}$转速旋涂第二层PDMS，并在${100}{}^{ \circ  }\mathrm{C}$固化${20}\mathrm{\;{min}}$完成电极封装。

4.2 织物基底制备。将Silbione(Factor II公司A-4717)$\mathrm{A}$与$\mathrm{B}$按1:1重量比混合${10}\mathrm{\;{min}}$。该混合物以1200转/分钟转速旋涂于聚四氟乙烯(PTFE)片材上1分钟，确保粘附层厚度均匀。表面覆盖棕色医用织物胶带(9907T，$3\mathrm{M}$)后，在${65}^{ \circ  }\mathrm{C}$烘箱中固化${30}\mathrm{\;{min}}$。固化完成后剥离PTFE片材。

4.3 电路与互连器制备。采用柔性PCB(fPCB)通过回流焊工艺安装所有电子元件。利用激光切割去除冗余区域以增强电路机械柔性。集成带滑动开关的锂聚合物电池组及圆形磁吸充电口实现供电管理。在集成电路下方放置低模量弹性体(Smooth-On Ecoflex Gel)隔离应变。整个电子系统采用额外弹性体(Smooth-On Ecoflex 00-30)封装，仅保留开关与充电口外露。

4.4 柔性传感器组装。将互连器焊接至电路后，通过涂覆并固化薄层硅胶，将软封装电子系统(电路部分)固定于织物基底面料侧完成组装。使用硅胶粘合剂(Gorilla)和织物网格将电极与魔术贴(VELCRO品牌)结合。图S9展示了由LIG电极、电路、互连件及魔术贴网格片组成的应变传感器集成制备流程。

4.5 柔性传感器表征。机械与电气性能测试装置包括:采用数字测力计(M5-5，Mark-10)配合电动测试平台(ESM303，Mark-10)评估机械性能，以及使用LCR表(Model 891，BK Precision)量化电阻值(图S10)。循环拉伸测试中，电极系统以${10}\mathrm{\;{mm}}/\mathrm{{min}}$速率进行垂直方向交替拉伸-释放，重复2000次；而阶梯式归一化电阻评估则以相同速率进行50次拉伸-释放。应变系数(GF)通过公式$\mathrm{{GF}} = \frac{\Delta R}{R \cdot  \varepsilon }$计算，其中${\Delta R}$表示电阻变化量，$R$为应变片标称电阻，$\varepsilon$为应变值。采用场发射扫描电子显微镜搭配能谱仪(FE-SEM/EDS，SU8230，日立)对所制备LIG电极的微观结构特征与元素组成进行表征，并通过拉曼光谱仪(Qontor共焦分散式拉曼光谱仪，波长${488}\mathrm{\;{nm}}$，雷尼绍)识别LIG图案中的特征峰(即D峰、G峰与2D峰)。另使用数码显微镜(VHX-7000，基恩士)进行简易观测。

4.6 固件数据处理与采集。全身运动追踪系统的数据处理由低功耗微控制器(nRF52832，Nordic Semiconductor)启动，该控制器搭载蓝牙片上系统以实现高效数据传输。通过串行外设接口(SPI)与低功耗模拟前端传感器ADS1292连接，并配合惠斯通电桥电路精确测量电阻值。系统采用18位模数转换器(ADC)传输数字信号，其输出十进制值对应实测电阻值与量程。nRF52832集成SoftDevice固件——这是针对无线通信协议优化的预编译二进制固件，支持通过低功耗蓝牙(BLE)将数据无线传输至定制应用移动终端(Galaxy Tab S8平板、iPad Pro第四代、Apple Watch 7及Galaxy Watch 5)。应变传感器电路由110mAh可充电锂聚合物电池供电，可持续工作${11}\mathrm{\;h}$以上。此外，8个可穿戴传感器通过微控制器单元(MCU)及外设的独立MAC地址进行标识(图S11)，该设计使移动系统能无缝实时采集处理多设备数据，构建完整运动监测体系。

4.7. 数据云计算接口与移动系统。系统初始连接8个设备，每个设备均配备擅长捕捉全身动作的应变传感器。这些多重${10}\mathrm{\;{hz}}$采样的应变传感器数据被汇总成批量数据文件，并以$5 - \mathrm{s}$为间隔无线传输至认证与计算服务器。数据上传通过FastAPI高效管理，该接口作为硬件与谷歌云平台(GCP)之间的桥梁。数据经预处理后转换为分段波形信息的(50,8)矩阵。输入数据经过标准化和归一化处理，用于学习模型的方差检测。在GCP内，深度学习模型通过专为快速处理设计的计算引擎，将数据与预定义物理动作或训练项目进行分类。对于请求和响应速度低于${200}\mathrm{\;{ms}}$的实时系统，可提供反馈和动作分类。为实现移动系统的高效反馈，我们选用Flutter Dart框架——其能编译为原生ARM或Intel x64代码，从而创建在Android和iOS基础设施上无缝运行的跨平台应用。不同于常依赖平台特定UI组件的React Native和Xamarin，Flutter支持跨平台视觉统一的UI/UX开发，无需此类依赖。其预编译(AOT)和即时编译(JIT)框架出色支持实时处理与异步流程。该应用配置为同时支持多传感器数据处理和异步处理，并编程实现远程可定制分析功能，既能提供即时分析洞察，又能生成日月统计数据以进行长期监测。

4.8. 全身动作分类。在我们利用应变信号与IMU信号研究全身运动模式时，采用了整合CNN与GRU层的深度学习架构。该模型基于Python的TensorFlow 2.0构建，在配备Intel i7处理器(I7-9750H)的笔记本上运行。IMU与应变信号(10Hz)按5秒分段，采用标准缩放法归一化为0到1之间的值。数据集按战略划分:${60}\%$用于训练，${20}\%$用于验证，剩余${20}\%$用于测试。架构始于适配信号形状的输入层，随后是两个卷积层:首层含64个滤波器(核尺寸3)，第二层含32个滤波器(核尺寸5)，每层均附带含4个滤波器(核尺寸4)的附加卷积层、PReLU激活和最大池化。这些层接入含64个单元的时间分布层，以及含40个单元的双向GRU层(循环丢弃率0.1，tanh激活)。后续层包括含96个单元及PReLU激活的全连接层、含160个单元及PReLU激活的另一全连接层、0.2丢弃层，以及采用softmax激活的输出层。优化过程使用固定学习率0.001的ADAM优化器，损失函数采用分类交叉熵。训练期间模型权重根据验证精度动态调整，超参数通过Keras Tuner随机搜索法选取。最终选取验证精度最高的模型进行预测能力测试(图S12与表S1)。

## 关联内容

## 数据可用性声明

支持本研究结果的数据可根据合理要求从通讯作者处获取。

## 5 支持信息

支持信息可免费从https://pubs.acs.org/doi/10.1021/acsami.4c17369获取。

图S1. LIG电极高度剖面图；图S2. LIG电极微观结构；图S3. 激光诱导石墨烯(LIG)拉曼光谱；图S4. 可穿戴IMU传感器；图S5. 各项训练中所有关节运动的IMU模式；图S6. 基于云的Android图形用户界面截图；图S7. LIG电极激光打印装置；图S8. LIG电极互连电镀装置；图S9. 可穿戴应变传感器制造流程；图S10. LIG应变传感器电极力学测试装置；图S11. 设备供电示意图、硬件结构、数据及实时运动监测系统；图S12. 全身运动分类机器学习架构；表S1. 全身运动分类机器学习层级信息(PDF)；视频S1. 实时全身动作识别(MP4)

视频S2. 应变传感器电子系统关键特性(MP4)

## 作者信息

## 通讯作者

Woon-Hong Yeo - 乔治亚理工学院可穿戴智能系统与健康中心、机械工程George W. Woodruff学院、生物医学工程Wallace H. Coulter系、Parker H. Petit生物工程与生物科学研究所、神经工程中心、材料研究所、机器人与智能机器研究所，美国佐治亚州亚特兰大30332；(C) orcid.org/0000-0002-5526-3882；邮箱:<EMAIL>

Jinwoo Lee - 东国大学机械、机器人与能源工程系，韩国首尔04620；邮箱:<EMAIL>

Jung Woo Sohn - 韩国国立金乌技术大学机械系统工程系，韩国龟尾39177；邮箱:<EMAIL>

## 作者

Kangkyu Kwon - 乔治亚理工学院电气与计算机工程学院，美国佐治亚州亚特兰大30332；乔治亚理工学院可穿戴智能系统与健康中心，美国佐治亚州亚特兰大30332；Yoon Jae Lee - 乔治亚理工学院电气与计算机工程学院，美国佐治亚州亚特兰大30332；乔治亚理工学院可穿戴智能系统与健康中心，美国佐治亚州亚特兰大30332；© orcid.org/0000-0002-4159-5966

Suyeong Chung - 乔治亚理工学院可穿戴智能系统与健康中心，美国佐治亚州亚特兰大30332；韩国国立金乌技术大学航空工程系、机械与电子融合工程系，韩国龟尾39177

Jimin Lee - 乔治亚理工学院可穿戴智能系统与健康中心及机械工程George W. Woodruff学院，美国亚特兰大
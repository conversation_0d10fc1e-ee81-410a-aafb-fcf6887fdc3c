Soft Smart Biopatch for Continuous Authentication-Enabled Cardiac Biometric Systems


# 摘要

生物识别锁定系统无缝集成了个人的生理特征与安全认证。然而，这些系统存在诸如误判认证、环境干扰以及多种认证方法各自的缺点等限制。为了解决这些限制，本研究开发了一种柔性智能生物贴片，用于持续采集心音的可穿戴心脏生物识别设备，认证误差低于0.5%。该设备设计隐蔽且用户友好，采用柔性生物兼容材料，确保舒适和易用性。贴片系统集成了一个微型麦克风，用于长时间和多维度的声音监测，增强了生物识别数据的可靠性。此外，利用机器学习算法，可以根据该低成本设备的连续监测属性为个人创建独特的识别密钥。这些优点使其比传统生物识别系统更有效和高效，有潜力提升移动设备和门锁的安全性。

# 1 引言

生物识别和安全系统是一组协调技术，收集个人信息并利用这些数据识别个人并解锁通道。这是三类安全措施中最安全的一种[1]，上述识别通过任何个人的生物特征进行。目前的保护措施分为三类：密码系统、可扫描的身份证件或个人生物识别系统。这些包括但不限于指纹、面部结构、视网膜特征、虹膜属性等。随着科学和工程的不断进步，迫切需要更好的安全和识别技术。安全措施，如同其他技术一样，必须跟上时代的步伐。然而，现有的生物识别技术存在局限性，其缺点造成了技术差距，高达1000美元的防伪认证设备就是例证。现有的指纹、手掌几何和语音识别生物识别技术可能被复制并受到环境变化的影响[2]。对于输入数据与已知数据之间的相关性要求的阈值必须补偿可能的环境因素[3]。目前，由于隐私问题，用户对面部识别存在大量抵触情绪，尽管它有其优势[4]。由于它扫描特定模式，在不利的光照情况下和随机反射下，虹膜扫描可能被高质量的已知用户眼睛图像所欺骗。由于这种生物识别技术比其他技术更昂贵，这些缺点加在一起使其成为价格过高的劣质技术。所有上述安全应用的最关键缺点是没有在多个安全维度上进行持续监控。行为生物识别技术已经通过智能手机、可穿戴设备和设施摄像头进行了持续认证的研究。这些行为生物识别技术的例子包括击键和触摸屏动态、眼球运动、步态、身体姿势和行为声音[5]。

然而，这些列举的生物识别技术会随时间而变化，例如个人的眼球运动动态和步态会随着一天中的时间、压力和其他环境因素而变化。对用户行为演变的研究已经提出，但由于缺乏训练数据集和成功的 AI 模型而证明困难[5]。另一个行为生物识别的例子是击键动态，机器学习模型从用户的原始击键数据中提取特征，以允许或阻止认证[6]。尽管这种方法通过辨别用户独特的打字倾向来增强典型密码的强度，但与其他行为生物识别一样，击键动态的准确性和持久性低于心音等生理特征[7]。生物识别技术的优势众多且显著。首先，生物识别数据难以伪造，提供了极好的安全性，因为只有已知用户才能使用这些系统获得进入权限。此外，它们不需要用户记忆或实际钥匙——生物识别技术使用户本身成为钥匙。使用这种类型的安全措施，用户永远不会遇到无法通过验证步骤的情况。另一个优势是生物识别系统的自动和简便识别。这些关键优势是这个技术时代最重要的，生物识别技术目前被认为是前沿技术[8]。它已经可以在办公楼和医院中看到[9]。

在此，我们介绍了一种可穿戴的持续心脏生物识别（CCB）贴片，以保留最先进的生物识别技术的优势，同时解决当前此类安全系统的一些问题。CCB 贴片展示了其作为一种非侵入性和持续生物识别工具的潜力。我们进行零相位和带通滤波，以在机器学习算法的秒级窗口中唯一区分每个心脏信号为独特波形，所有这些都集成在移动应用程序中。此外，我们的无线数据采集单元采用低能耗蓝牙模块和生物兼容粘合剂，适合长期生物识别系统作为可穿戴设备。本研究集合了其他识别系统的资格，并在能够长时间连续和远程监控心音生物识别方面显示出新颖性，同时比目前用于电话安全和门锁应用的替代产品显著便宜。

# 2 结果与讨论

## 2.1 设备设计和功能概述

CCB 贴片的优势来源于其跨学科能力和新技术。该设备的设计需要精确的纳米制造，以提供其应用所需的关键属性，例如提高其移动性、确保设备寿命和优化组件放置以实现适当的听诊。此外，CCB 贴片的精细纳米制造为远程患者心肺听诊开辟了一条新途径（**图1A**）。**图1B**显示了组成整个系统的两个机械柔性无线组件之一的爆炸图。设备本身是一个柔性贴片，在 KiCAD 中设计（见**图 S1**和**表 S1**，支持信息），其多层铜导线连接不同的电路板部分。电路的分层设计在制造和包装中是必不可少的，因为它减少了表面积，这对于需要持续听诊的任何患者来说都至关重要，并且几乎没有疼痛。设备的移动性是一个显著的优势，来自贴片的结构。作为柔性设备，贴片具有可塑性和弯曲性。其设计的另一个部分还增加了设备限制运动伪影的能力，并通过内层硅胶衬里（见**图 S2**，支持信息）在弹性体外壳中弯曲。弹性体帮助皮肤贴合性，确保与使用设备的用户的娇嫩皮肤和高度弯曲的解剖特征兼容。评估了其他商业医用胶带的水汽透过率（MVTR），结果显示 CCB 贴片使用的医用胶带的 MVTR 最高，明显优于评估的其他行业标准材料（见**图 S3**和**表 S2**，支持信息）。长时间监测测试的数据表明，在两个半小时的记录过程中，数据点一致，信噪比在设备的重复和长时间使用过程中变化很小（见**图 S4A**，支持信息）。在长时间使用期间，可穿戴设备表面常会积聚汗水或死皮细胞。然而，我们的 CCB 贴片表现出性能仅有微小下降，衰减水平从 21.07 dB（见**图 S4B**，支持信息）降至 20.68 dB（见**图 S4C**，支持信息），这意味着贴片的透气性足以在长时间佩戴设备期间不引起刺激。设备在皮肤上的放置前后图像显示没有出现任何干扰（见**图 S5**，支持信息）。所有这些测试的出色结果表明，CCB 贴片可以承受在患者身上使用时可能遇到的大多数环境。设备的移动性允许由于其随用户移动而不是用户移动麦克风，因而提高了数据质量。弹性层的柔性和皮肤贴合接触对于持续生物识别至关重要。移动性结合持续记录，能够在多个识别层和长时间内无缝记录和识别个人。其理念的一部分是消除每个访问点需要重新扫描的需求，而其他生物识别技术需要另一次扫描。持续记录可以通过心脏周期的变化来识别未经授权的个人，这些变化在长时间内不会改变，比一次性的生物识别扫描更难模仿。将电路应用于电路板是快速、高效且更重要的是有效的。CCB 贴片使用紧凑的锂离子聚合物电池（40 mAh），根据当前设计提供24小时的运行时间，功耗为6.3 mW。为了优化电池使用，固件可以集成各种省电技术，例如常开或待机模式。在待机模式下，设备在未连接到主机设备时显著降低数据采样率。触发后，它恢复正常采样率以有效节省电池电量。同样，如果需要长达一周的扩展认证系统使用，可以将 CCB 贴片设计修改为容纳更大的 250 mAh 电池，以进行一周的记录。为了引导电池连接，电路的电源垫连接到电池。电池位置确保温和地放置在胸部弯曲的皮肤上，固定在设备顶部。电池使其能够通过蓝牙低功耗（BLE）系统芯片和相关的传感器集对心脏活动进行听诊。来自 MEMS 麦克风的模拟信号通过前置放大器，然后移动到 ADC 以转换为数字信号，以便通过蓝牙无线传输到移动设备（**图1C**）。从设备获取的原始信号将通过卷积神经网络（CNN）训练处理，记录心脏活动，并最终输入到使用心音的二次认证中。提取的特征将在模型中训练以认证个人。这个过程流程在**图1D**中简化了。设备的标准生物识别质量、移动性、远程和持续记录、个人心音变化的可能性极低以及几乎不可能伪造的特点结合在一起，创建了一个安全、易用的生物识别安全系统。

![[../../../../../6、知识库/文献知识/4、柔性系统/实验借鉴/国际/团队/Woon-Hong Yeo/2023/用于连续认证启用心脏生物识别系统的软智能生物贴片/图片/Pasted image 20240715163025.png]]
**图1**  
概述使用连续认证的心脏生物识别系统。A) 一名佩戴连续心脏生物识别（CCB）贴片的受试者的插图，以及实际设备顶部和底部视图的特写（左上照片）和使用移动应用程序解锁手机和门锁的控制系统。B) 贴片的爆炸视图，显示了多层沉积材料、集成芯片和麦克风。C) CCB 贴片在电源、数据、硬件结构和实时安全系统方面的示意图。D) 描述整个生物识别系统的流程图，从 CCB 贴片到通过机器学习分类算法控制手机和门锁。

## 2.2 连续监测中的生理学和机械声学

分析心脏声音及其独特特征构成了该生物识别技术的基础。心脏瓣膜的开闭会产生心脏声音。心脏有几种不同的声音，包括 S1、S2、S3 和 S4[10]，但对于正常受试者，设备的关注点是 S1 和 S2。S1 声音由二尖瓣和三尖瓣关闭时产生，如**图2A**所示。这个周期的时期称为收缩期[11]。当肺动脉瓣和主动脉瓣在舒张期关闭时，产生 S2 声音，这是两者中较响亮的声音，这是舒张期的开始。平均收缩期和舒张期的持续时间分别为 0.35 和 0.45 秒，总共一个心动周期约为 0.8 秒[11]，如**图2B**中 4 位代表性参与者的数据所示。完整的心动周期作为 S1 和 S2 与压力和体积变化协调可见于**图2C**和**图2D**。心脏声音的频率范围为 20 - 220 Hz[12]。



![[../../../../../6、知识库/文献知识/4、柔性系统/实验借鉴/国际/团队/Woon-Hong Yeo/2023/用于连续认证启用心脏生物识别系统的软智能生物贴片/图片/Pasted image 20240715163032.png]]
**图2**  
连续监测中的生理学和心脏系统的机械声学。A) 心脏瓣膜和心音图（PCG）原理在生理学中的示意图。B) 四位代表性参与者的时间序列原始PCG数据中的S1和S2峰值。S1：二尖瓣和三尖瓣关闭，S2：半月瓣（主动脉和肺动脉）关闭。C) 心脏生理学中压力变化的图示。发生顺序：1. 等容收缩，2. 射血，3. 等容舒张，4. 快速摄取，5. 舒张，6. 房收缩；重要事件：i. 房室瓣关闭，ii. 主动脉瓣打开，iii. 主动脉瓣关闭，iv. 房室瓣打开。D) 心脏生理学中一次循环中正常心室容积的图示。E) 收缩期和舒张期与心脏压力和心室容积同步的时间序列PCG数据。


设备的音频分辨率足以识别心脏发出的特定声音[13]。这些特定声音包括心脏声音的几个方面，如强度、形状、S1和S2之间的相对大小，以及心动周期时间长度的标准差。影响心脏声音的其他因素包括声音在心脏和胸腔内的回响。个体差异显著，取决于心脏的激活顺序、传导性和心脏质量方向[14]。例如，心脏脂肪多的人心音可能较小，而胸腔大的人则可能有更深的频率声音。一旦测量和量化了个体的心率变异性，下一步就开始了。CCB贴片的原始数据集首先经过数字信号处理。应用的两个滤波器包括3阶Kaiser窗的零相位滤波器用于幅度归一化，以及设置为切断心脏频率范围之外的频率（20-250 Hz）的带通滤波器。应用滤波器后，生成一个新的干净数据文件，然后将信息发送到机器学习程序。通过CCB贴片获取的精确和高质量数据并通过广泛的数字信号处理进行清理后，计算机可以生成具有规范的个人资料[15]。如果算法在传入的数据流中注意到已知特征，它可以识别该特定数据集的所有者。时间序列中的心音，S1和S2可见且可区分，如**图2E**所示，与两个完整心动周期的压力变化和心室容积同步。这种时间序列图是机器学习在滤除外部噪声干扰后的结果。数据集的纯度反映了设备数字信号处理和噪声抑制算法的有效性。
## 2.3 可穿戴贴片的机械特性

对CCB贴片进行的机械测试结果如**图3**所示。对设备进行弯曲/拉伸测试表明，在受试者穿戴设备进行各种动作时，施加应力并不会显著改变电阻值，如**图3A**所示。为了模拟拉伸和弯曲，进行了循环测试，测量电路两端的电阻变化，以确保实际监测期间的数据可靠性（见**图S6**，支持信息）。**图3B**显示了拉伸测试结果，**图3C**显示了使用数字测力计（EMS303，Mark-10）和万用表（DMM7510，Tektronix）进行的循环机械测试的弯曲结果。在100个循环中，电阻波动小于20 mΩ。循环拉伸测试的总电阻变化约为0.53 mΩ，循环弯曲测试的总电阻变化约为0.64 mΩ。计算有限元分析也捕捉了**图3D**和**图3E**中的拉伸和弯曲结果，分别为180度弯曲和20%拉伸[16]，显示每个电路部分的应变小于1.5%。与商业医用胶带相比，CCB在长时间内保持了最高的压力，这意味着它保持了最佳的皮肤接触（见**图S7**，支持信息）。在防水测试中，CCB贴片的性能优于预期，水下的数据质量仅稍微有噪音，设备一旦拿出水外，噪音就消失了（见**图S8**，支持信息）。设备贴合皮肤，在各种条件下保持其功能。即使在水流和水渗透皮肤下，设备也能准确检测径向脉搏。此外，其性能在不同体温范围内保持一致。这表明其稳健性和可靠性，表明其在多种应用中的潜力。

![[../../../../../6、知识库/文献知识/4、柔性系统/实验借鉴/国际/团队/Woon-Hong Yeo/2023/用于连续认证启用心脏生物识别系统的软智能生物贴片/图片/Pasted image 20240715163038.png]]
**图3**  
柔性贴片的机械特性。A) 佩戴CCB贴片的用户在运动中设备的拉伸、弯曲和压缩的示意图。B) 通过100个循环20%拉伸的电阻变化结果。C) 150个循环3毫米半径180°弯曲期间的电阻变化结果。D) 90°和180°弯曲的有限元分析及电路板封装的实际照片，放大视图显示应变最大的各个区域。E) 20%拉伸的有限元分析，放大视图显示应变最大的各个区域。

## 2.4 机器学习架构和性能

为了对来自CCB贴片的心脏数据进行预处理，进行了20Hz到250Hz的带通滤波和零相位滤波（**图4A**），以减少信号中的噪声并保留时间和相移问题，这在基于时间序列的机器学习训练中至关重要。**图4B**展示了来自CCB贴片的示例心脏信号的1秒窗口，包括原始、带通滤波和零相位滤波图。尽管带通滤波消除了设备引起的直流偏移，但不能完全减少基线噪声，而零相位滤波器以所有频率为零的相移滤除这些噪声。在CNN架构中，使用了实验室数据集中的20名受试者的数据。数据集分为三个部分：60%用于训练（40,075个epoch），20%用于验证（13,625个epoch），20%用于测试（13,625个epoch）。在每次训练迭代期间，根据模型的训练验证准确性调整CNN网络参数的权重。大多数超参数值，如学习率、核大小、每个卷积层的滤波器数量和每个dropout的单元数，都是通过随机搜索方法确定的。最终，选择验证准确率最高的模型作为最佳模型。评估该顶级模型的性能基于测试数据集的预测准确性。因此，所采用的CNN架构在初始块中使用了（3,1）池大小的2D最大池化、25个滤波器和（10,1）核大小的两个2D卷积层。为了减轻过拟合，加入了批量归一化。对于第二块，使用了25个滤波器和依次为（10,25）、（10,50）和（10,100）的三个单独卷积单元（Conv_1,2,3），以及（3,1）池大小的2D最大池化（参见**图4C**和**图4D**）。机器学习模型中应用了一系列层，如**图4E**、**图S11**和**表S3**（支持信息）所示。该模型经过训练，确保每位参与者的心音波在时间序列中每对S1和S2段具有不同的模式和形式。由于参与者的平均心率约为60，每位参与者的类别输入2秒120个样本[15]。模型中训练的每位参与者不同波形的混淆矩阵如**图4D**所示，证明模型正确检测每位参与者的心音。正如提议的生物识别准确性或99.55%的正确识别率所示，心音生物识别提供了解决这些困难的方案，尽管其误差率为0.45%。与**表1**中的其他设备相比，本研究显示了最佳的准确性。建议的心音生物识别优于指纹、签名和语音识别的估计误差率。在记录的时间内，一个人的心音几乎没有改变的可能性。心音难以伪造，除非未经授权的用户试图克隆与另一人的心脏完全相同并将其放置在类似回响的胸腔内。与面部识别软件、指纹识别和其他生物识别安全措施带来的问题相比，测量个人的心音可能是一个不那么侵入性的替代方案。持续获取个人心音的能力是最显著和明显的优势，因为它在许多安全层中创建了识别。目标是消除在每个入口点重新扫描生物识别的需求。CCB贴片是一种高效、适应性强、不显眼且准确的技术，可以分析人体中的一个重要生物识别特征——心音。



![[../../../../../6、知识库/文献知识/4、柔性系统/实验借鉴/国际/团队/Woon-Hong Yeo/2023/用于连续认证启用心脏生物识别系统的软智能生物贴片/图片/Pasted image 20240715163043.png]]
**图4**  
CCB贴片20名参与者数据的机器学习架构和结果。A) 原始CCB数据过滤阶段流程图的预处理概述。B) 原始、带通滤波和零相位滤波数据的1秒窗口图。C) 整体心脏生物识别分类的流程图。D) 训练模型的混淆矩阵，基于20名参与者数据，准确率为99.55%。E) 基于CNN的心脏生物识别分类的机器学习架构。

表1. 各种生物识别系统性能比较

| 参考文献        | 连续验证 | 准确率  | 形态因素  | 数据记录 | 记录条件      | 分类方法                            |
|----------------|----------|--------|----------|---------|-------------|------------------------------------|
| 本工作          | 是       | 99.55% | 全软      | 无线     | 静止和运动    | 卷积神经网络 (CNN)                 |
| [19]           | 否       | 96%    | 刚性      | 无线     | 静止         | 矢量量化 (VQ) 和高斯混合模型 (GMM) |
| [20]           | 否       | 89.60% | 刚性      | 无线     | 静止         | VQ                                 |
| [6]            | 否       | 94.40% | 刚性      | 有线     | 静止         | VQ                                 |
| [21]           | 否       | 91%    | 刚性      | 无线     | 静止         | 欧几里得距离 (ED)                  |
| [22]           | 否       | 高达99% | 刚性      | 有线     | 静止         | 相似度距离                         |
| [23]           | 否       | 95%    | 刚性      | 有线     | 静止         | 支持向量机 (SVM)                   |
| [24]           | 否       | 91.05% | 不适用    | 开放数据库 | 静止         | SVM                                |
| [25]           | 否       | 97.5%  | 不适用    | 开放数据库 | 静止         | SVM                                |
| [26]           | 否       | 93%    | 不适用    | 开放数据库 | 静止         | K-最近邻 (KNN)                     |
| [27]           | 否       | 98.67% | 不适用    | 开放数据库 | 静止         | ED                                 |

解释:
- 形态因素: 指的是设备的结构类型（刚性或软性）。
- 数据记录: 记录数据的方式（无线、有线或开放数据库）。
- 记录条件: 记录数据时的状态（静止或运动）。
- 分类方法: 使用的分类算法（如卷积神经网络、矢量量化、欧几里得距离等）。



## 2.5 贴片在移动设备和门锁上的安全应用

**图5A** 显示了使用我们的CCB的整体安全系统。数据传输结构简单：用户胸骨上的可穿戴设备、移动设备和无线门锁系统。在本研究中，三个设备按顺序工作以进行安全的生物识别认证：一个带有线性执行器的RF接收器锁，用于解锁锁，一个集成了BLE开发套件的RF遥控器（**图5B**）。最后，移动设备作为主要设备来计算认证系统，如**图5C**所示。视频S1（支持信息）中演示了这种安全应用。系统通过Jetpack Android Studio库中的Android Keystore API应用安全功能，用于加密和解密（或存储和访问）生物识别数据输入/输出流，从而允许数据的安全处理。CNN模型嵌入在带有TensorFlow的Android应用程序中。CNN模型可以实时提取S1和S2数据，以进行用户特征检测和认证（见**图S12**，支持信息）。应用CNN模型后，将注册新用户的处理数据以进行认证。为了评估CCB系统的性能和安全文件流结构，我们开发了一种信号分类机制，以评估CNN的连续输入心脏数据。分类器基于序列匹配和异常检测，可以全面预测类标签和信号特征的相似性。应用后端解密模型并比较用户信号特征（见**图S12**，支持信息）。信号分类器输出将作为元数据无线通信到控制设备，如**图5C**所示。来自移动设备的连续数据包的编码元数据通过个体UUID特征上的可读/可写BLE结构进行广播。由于来自移动设备的输出是决策信号，因此它被安全地编码。只有在控制设备固件中才能解码密钥对解码器。在控制设备内部，模型输出的认证用作决策信号数据，如**图5D**所示。微控制器中的固件结构解码信号并验证模型分类。嵌入式验证算法使控制设备将成功日志记录到内部板，并向无线门锁发送控制信号。此外，控制设备向移动设备发送可写字节信号，以额外记录成功认证日志，如**图5E**所示（详细信息见**图S12**，支持信息）。最后，无线门锁接收配对的控制设备信号，并控制电机驱动器锁定或解锁门。CCB接口显示了适用于广泛应用的安全硬件和软件接口。视频S2（支持信息）显示了注册用户成功解锁门的演示。此外，由于第二个用户不是ML算法的20个训练数据之一，CCB系统识别为未注册个人尝试解锁门。如果我们扩大样本量，准确性将提高并保持接近100%的准确率。所展示的工作可以找到使用可穿戴心脏生物识别系统的其他应用（见**图S13**，支持信息），包括机密文件传输、银行安全、双因素认证和安全的远程患者监测。



![[../../../../../6、知识库/文献知识/4、柔性系统/实验借鉴/国际/团队/Woon-Hong Yeo/2023/用于连续认证启用心脏生物识别系统的软智能生物贴片/图片/Pasted image 20240715163048.png]]

**图5**  
移动设备和门锁的应用。A) CCB用户使用注册的心脏数据解锁手机以及使用移动应用程序解锁门的示意图。B) 门锁系统的实际照片，集成了遥控器的电路将信号发送到门锁执行器。C) 门锁解锁系统的控制系统和物流示意流程图。D) 硬件中控制设备的详细示意流程图。E) 移动应用程序中注册用户和未注册用户的屏幕截图。



# 3 结论

本研究介绍了用于记录心音和集成生物识别锁定机制的可穿戴柔性 CCB 贴片的开发。我们从20名个体中收集了心音，并通过两阶段信号处理管道处理这些数据，以消除干扰和伪影。此外，我们探索了使用深度学习技术预测心脏信号的 CCB 贴片系统的潜力，达到了99.55%的准确率。这些发现证明了 CCB 贴片在实际应用和安全性方面优于现有生物识别系统。将生物识别功能集成到可穿戴贴片中，提供了比传统基于密码的认证更可靠和安全的替代方案，增强了虚拟现实交易和数字安全应用的安全性。通过利用生物识别技术，CCB 贴片消除了对强大用户记忆或实体钥匙的需求，具有抗复制性。CCB 贴片的快速、免提和远程识别能力为各种应用开辟了新的可能性。未来的研究将集中于提高耐用性，将贴片的生物识别安全功能与健康监测功能集成，实现无缝融入日常活动，提升整体福祉。CCB 利用深度学习策略并根据获取的信息预测心脏信号的能力具有巨大的潜力，特别是对于有心脏病的个体。认识到其重要性，我们的系统可能对心脏疾病的诊断和监测产生影响。

# 4 实验部分

## 设备制造
使用 KiCAD 软件设计了一种用于可穿戴听诊器传感器的柔性印刷电路板（FPCB）。整个设备在最宽处为53 mm × 25 mm。FPCB 的铜线在板表面结束于铜垫上，纳米电路被放置在相应的铜垫上，并使用焊膏和热板焊接（见**图 S2A**，支持信息）。这一精细过程是为了确保设备功能的持久性。使用一层软性弹性体凝胶作为可穿戴听诊器与皮肤的基底粘附层。将20克 Eco-flex 凝胶 A 和 B 混合物倒入150毫米直径的培养皿中，并以1000 rpm 旋转涂覆5秒。这确保了均匀层与皮肤接触而不会对电路造成变形。从培养皿中固化的凝胶层中切出粗略边界，并将集成电路板放置在凝胶层顶部。在麦克风岛的中间切了一个孔，以允许声音穿透弹性体层（见**图 S2B**，支持信息）。通过混合1:1比例的 Eco-flex 30 A 和 B 在整个电路板顶部倒入另一层软性硅胶弹性体，覆盖凝胶层的边缘，完全封装设备。最后，在织物层上使用高粘性硅胶凝胶，以1000 RPM 旋转涂覆30秒，并在60°C 的热板上固化15分钟。将织物切成圆形放置在封装的麦克风岛顶部，以更好地对麦克风施加压力（见**图 S2C,D**，支持信息）。

## 硬件设计
CCB 贴片的设计中使用了多种电子组件。使用了电阻、电容和电感等无源组件，以及 BLE SoC（nRF52832，Nordic Semiconductor）、德州仪器的 ADS1292模拟前端芯片、TDK Invensense 的 ICS40212 MEMS 麦克风芯片、ST Electronics 的 TS472音频放大器芯片，最后是 TPS63001开关稳压芯片为整个电路供电。

## 机械研究
对设备进行了以下机械测试，以确保其在各种环境中的性能：弯曲/拉伸测试、SNR 测试、接触压力测试、防水测试、透气性测试和刺激性/持续时间测试。使用 ESM303（Mark-10）机器进行拉伸/弯曲测试；在记录电阻的同时拉伸设备，以查看是否对设备的效能有影响。同样进行了弯曲测试。此外，还测试了设备的粘附材料，以确定其在皮肤接触中保持足够高的压力水平并提供可接受的信噪比（SNR）。防水测试是所有测试中最简单的。它包括开始记录、将设备浸入水中、取出设备，并查看其是否能够继续记录（见**图 S8**，支持信息）。最后，通过将均匀容器注满水并使用不同类型的医用覆盖物密封进行透气性测试，以开口容器作为对照。透气性较高的医用覆盖物填充的容器中的水蒸发得更快，因此在七天未动后，透气性最高的材料将是最空的（见**图 S3**，支持信息）。最后，通过收集2.5小时的心脏数据并检查设备放置处的皮肤（以评估刺激性）和收集的数据质量（信噪比）进行了刺激性/持续时间测试（见**图 S4**和**图 S5**，支持信息）。

## 数据收集与过滤
CCB 贴片安装在稍靠近胸骨左侧的位置，以优化心音采集，并在20名不同参与者上进行了测试[6]。每次会话的持续时间约为60秒。受试者在采集过程中保持静止和坐姿。贴片获取原始心脏信号，并将缓冲数据无线传输到计算机或移动设备。信号与时间戳同步，并通过 CSV 等文件保存，准备进行分析。首先，原始信号经过信号处理，生成用于训练和 CNN 分类器的数据纯净形式。在信号处理和平滑处理之后，初步数字声音数据被预处理用于机器学习。最后，信号可以作为 CNN 模型中的个人资料进行分析。

## 心音分类
数字化声音数据以每秒4000个样本的速度记录，专业人类分析员对相应数据进行分段以进行训练。然后将这些数据片段输入机器学习算法（使用 Python 的 TensorFlow），并为每个人创建档案。在他们的档案中，机器被训练以了解他们的心音如何随时间变化，以及对他们来说有什么特定之处[17]。在训练过程中，通过调整每类样本的数量以保持平衡的数据集，从而尽量减少分类偏差。模型架构通过一系列迭代改进，参考先前的研究设计[18]。该模型基于 CNN 架构的输入数据设计，特别是用于时间滤波信号数据。CNN 架构的输入由2秒的原始信号数据组成，大小为8000×1。为了预处理 CNN 架构的数据集，使用快速傅里叶变换（FFT）和最小最大缩放来提取特征。选择的非线性激活函数是泄漏整流线性单元（Leaky ReLU）。CNN 架构使用 ADAM 优化器进行优化（学习率=0.001，ß1=0.9，ß2=0.999），批量大小为64。为了避免过拟合，通过随机排除30%的数据集作为验证集在优化阶段开始时指定为验证集。如果验证损失未能改善，学习率将按5倍衰减。如果连续两次学习率衰减而网络性能在验证集上没有改善，则终止训练。CNN 架构包含两个不同的卷积块。初始块由一对2D 卷积层、批量归一化层和2D 最大池化层组成。第二块具有三个单独的卷积单元（Conv_1,2,3）。每个单独的卷积单元（Conv_N）包含一个卷积层、一个批量归一化层、一个 Leaky ReLU 层和一个最大池化层。卷积神经网络的最终输出是一个20×1向量，然后通过一个 softmax 层，生成与20个参与者之一相对应的预测类别。

## 生物识别认证
开发了一个实时生物识别认证平台，在移动设备中处理实时 CNN 模型。使用 Tensorflow（v2.8）和 Tensorflow lite 处理 CNN 模型输出的安全快速深度学习框架。TF 用于部署设计的数据流结构，通过处理从 CCB 获取的信号数据计算输出数组。TF lite 序列化工具包嵌入在 Android Kotlin 应用程序中（Kotlin v 2022.1.7），并促进 CNN 算法的实施。用于 ML 训练的信号处理系统转换为 C++，以便快速框架处理实时输入。自定义分类器机制全面分析段落顺序，检测输入过滤信号的 S1、S2、特征检测和异常以及 CNN 类（见**图 S11**，支持信息）。在用户输入锁定和解锁按钮后，提取的心脏数据与注册认证数据进行比较，生成信号分类器输出。CNN 模型的输出层用于实时用户生物识别

数据。通过应用内的训练和注册认证数据评估提取的心脏数据。平台利用 Jetpack Android Studio 库（Compose v1.1）中的 Android Keystore API 进行模型编码/解码、密钥对匹配和模型加载过程（见**图 S11**和**图 S12**，支持信息）。为每个应用程序发布生成并记录主密钥以增加保护。输出通过字节算法以元数据形式无线发送，仅在控制设备上解码。控制设备板上接收到的元数据为 nRF52832和 BLE 平台的十六进制类型。控制设备仅接收来自移动设备的正确外设信号（匹配特定服务 UUID 和字节解码器）并使用接收到的安全密钥解码输出。嵌入式评估算法在 nRF52832中评估类模型并做出认证决策。最后，控制设备将控制信号传输到集成有线性执行器（Actuonix）的门 RF 接收器。出于安全考虑，控制设备记录成功试验的日志，并通过可写特性 UUID 将成功信号返回给移动设备。这允许将来调查跟踪认证历史。配对的控制设备只能启动最终的无线门锁系统。门锁接收特定的关键信息以解锁门。

## 人体研究
人体试点研究涉及多名健康志愿者，研究遵循佐治亚理工学院批准的 IRB 协议（ H21038 ）。所有参与者都同意并签署了同意书，允许进行实验程序。







![[../../../../../6、知识库/文献知识/4、柔性系统/实验借鉴/国际/团队/Woon-Hong Yeo/2023/用于连续认证启用心脏生物识别系统的软智能生物贴片/图片/Pasted image 20240715171932.png|500]]
**图S1**. 印刷电路板布局及焊盘。A) 主要集成芯片组件的PCB实物照片。B) 印刷电路板的3D生成布局图（顶部视图和底部视图）。




![[../../../../../6、知识库/文献知识/4、柔性系统/实验借鉴/国际/团队/Woon-Hong Yeo/2023/用于连续认证启用心脏生物识别系统的软智能生物贴片/图片/Pasted image 20240715171940.png|500]]
**图S2**. 设备集成和制造步骤。A) 从裸露的柔性PCB开始，将芯片组件焊接在顶部。B) 使用Ecoflex 30的设备照片，整个顶部和电池均被封装。C) 顶部带有silbione层的最终CCB贴片。D) 2D侧视图示意图。E) silbione层下整个设备结构的3D示意图。



![[../../../../../6、知识库/文献知识/4、柔性系统/实验借鉴/国际/团队/Woon-Hong Yeo/2023/用于连续认证启用心脏生物识别系统的软智能生物贴片/图片/Pasted image 20240715171950.png|500]]
**图S3**. CCB贴片粘合剂透气性测试。A) 使用装满水的玻璃罐测试各种粘合剂，并比较通过粘合剂的气态H2O的透气性，分别是开放、3M Tegaderm、3M 2476P、Silbione和Micropore。B) 室温监测7天后的透气性最终结果。与其他粘合剂相比，CCB的silbione层显示出最佳透气性。




![[../../../../../6、知识库/文献知识/4、柔性系统/实验借鉴/国际/团队/Woon-Hong Yeo/2023/用于连续认证启用心脏生物识别系统的软智能生物贴片/图片/Pasted image 20240715171957.png|500]]
**图S4**. CCB多小时记录。A) 设备配备40mAh电池，在实验室中监测心音2.5小时。B和C) 不同时区10秒窗口的放大图。数据即使在长期内也显示出类似的趋势，确保与皮肤的贴合性。



![[../../../../../6、知识库/文献知识/4、柔性系统/实验借鉴/国际/团队/Woon-Hong Yeo/2023/用于连续认证启用心脏生物识别系统的软智能生物贴片/图片/Pasted image 20240715172004.png|500]]
**图S5**. 3天的生物相容性和长期佩戴。A) 受试者佩戴CCB设备的第一天照片。B) 佩戴CCB设备3天后胸部皮肤的照片。显示设备附着区域的皮肤有些发红，但没有出现刺激。




![[../../../../../6、知识库/文献知识/4、柔性系统/实验借鉴/国际/团队/Woon-Hong Yeo/2023/用于连续认证启用心脏生物识别系统的软智能生物贴片/图片/Pasted image 20240715172010.png|500]]
**图S6**. CCB贴片的循环机械测试。A) 在两端夹住CCB，并测试电路中20%拉伸通过150个循环的电阻变化。B) 将CCB固定在两个玻璃片上，夹在Mark-10上，并测试150个循环3毫米半径180°弯曲的电阻变化。



![[../../../../../6、知识库/文献知识/4、柔性系统/实验借鉴/国际/团队/Woon-Hong Yeo/2023/用于连续认证启用心脏生物识别系统的软智能生物贴片/图片/Pasted image 20240715172017.png|500]]
**图S7**. 各种粘合层对皮肤施加压力。使用生物相容性粘合剂（如Micropore、3M Tegaderm、3M 2476P和CCB silbione层）对设备施加不同的压力。CCB silbione显示对皮肤施加的压力最高。




![[../../../../../6、知识库/文献知识/4、柔性系统/实验借鉴/国际/团队/Woon-Hong Yeo/2023/用于连续认证启用心脏生物识别系统的软智能生物贴片/图片/Pasted image 20240715172024.png|500]]
**图S8**. CCB贴片的防水测试。A) 将CCB贴片连接到一个移动应用程序，该应用程序在设备浸入水中前后测量手腕的桡动脉脉搏。B – D) 会话期间防水测试的图表。即使在流水期间和停止后，桡动脉脉搏波形仍然显示，并且S1和S2峰值清晰可见。


![[../../../../../6、知识库/文献知识/4、柔性系统/实验借鉴/国际/团队/Woon-Hong Yeo/2023/用于连续认证启用心脏生物识别系统的软智能生物贴片/图片/Pasted image 20240715172031.png|500]]
**图S9**. 零相位滤波阶段。A) 输入信号x[n]进入卷积：z[n] = x[n]*h[n]，其中使用直接傅里叶变换（DFT）的时间反转特性与h[n]卷积。v[n]现在被时间向后滤波，以获得正确顺序的输出y[n]。B) 针对20Hz至220Hz的心音，实施了一阶高通和低通滤波器作为带通滤波器，以消除20Hz以下和250Hz以上的频率。



![[../../../../../6、知识库/文献知识/4、柔性系统/实验借鉴/国际/团队/Woon-Hong Yeo/2023/用于连续认证启用心脏生物识别系统的软智能生物贴片/图片/Pasted image 20240715172037.png|500]]
**图S10**. 零相位滤波心音图和功率谱密度。A) 6秒窗口的零相位滤波心音，中间的S1和S2对之间的噪声极少。B) 0到500Hz的功率谱图，显示频率序列图和每个频率的功率密度幅度。C) S1和S2峰值在频率和相应归一化幅度的1秒窗口瀑布图。








![[../../../../../6、知识库/文献知识/4、柔性系统/实验借鉴/国际/团队/Woon-Hong Yeo/2023/用于连续认证启用心脏生物识别系统的软智能生物贴片/图片/Pasted image 20240715172044.png|500]]
**图S11**. 心脏生物识别分类的机器学习架构：提出的心脏生物识别分类深度神经网络架构。


![[../../../../../6、知识库/文献知识/4、柔性系统/实验借鉴/国际/团队/Woon-Hong Yeo/2023/用于连续认证启用心脏生物识别系统的软智能生物贴片/图片/Pasted image 20240715172052.png|500]]
**图S12**. 安卓应用程序结构。A) 移动应用程序片段，显示注册用户从弹出窗口解锁1号门。B) 应用程序内部过滤后的注册用户S1和S2对的音频。C) 0到500Hz窗口中的Welch窗口功率谱密度图，其中S1和S2峰值在80和160Hz显示出明显的峰值。D) 应用程序后台流程图，模型比较并匹配解密后的模型以匹配用户。E) 当应用程序后台无法与注册数据比较和匹配时，未注册用户的应用程序片段。


![[../../../../../6、知识库/文献知识/4、柔性系统/实验借鉴/国际/团队/Woon-Hong Yeo/2023/用于连续认证启用心脏生物识别系统的软智能生物贴片/图片/Pasted image 20240715172059.png|500]]
**图S13**. CCB贴片的未来应用。A) 在机密文件传输解锁或高层银行安全中使用的连续心脏认证系统的安全应用。B) 多级验证在双因素认证（2FA）或CTP系统中的使用。C) 用于医院或家中的连续心脏监测的临床或远程监测应用。






![[../../../../../6、知识库/文献知识/4、柔性系统/实验借鉴/国际/团队/Woon-Hong Yeo/2023/用于连续认证启用心脏生物识别系统的软智能生物贴片/图片/Pasted image 20240715172145.png|500]]





![[../../../../../6、知识库/文献知识/4、柔性系统/实验借鉴/国际/团队/Woon-Hong Yeo/2023/用于连续认证启用心脏生物识别系统的软智能生物贴片/图片/Pasted image 20240715172152.png|500]]





![[../../../../../6、知识库/文献知识/4、柔性系统/实验借鉴/国际/团队/Woon-Hong Yeo/2023/用于连续认证启用心脏生物识别系统的软智能生物贴片/图片/Pasted image 20240715172159.png|500]]




![[../../../../../6、知识库/文献知识/4、柔性系统/实验借鉴/国际/团队/Woon-Hong Yeo/2023/用于连续认证启用心脏生物识别系统的软智能生物贴片/图片/Pasted image 20240715172207.png|500]]




![[../../../../../6、知识库/文献知识/4、柔性系统/实验借鉴/国际/团队/Woon-Hong Yeo/2023/用于连续认证启用心脏生物识别系统的软智能生物贴片/图片/Pasted image 20240715172219.png|500]]




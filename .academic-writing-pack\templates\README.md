# 学术写作模板系统 (Academic Writing Template System)

## 📁 模板结构概览

### **核心写作模板** (7个)
```
01-abstract-template.md           # 摘要模板 (支持医学/技术双导向)
02-introduction-template.md       # 引言模板 (新增技术演进逻辑链和地域差异分析)
03-materials-methods-template.md  # 材料方法模板 (支持制造/功能双导向)
04-results-template.md           # 结果模板总指导 (含子模板选择指南)
05-discussion-template.md        # 讨论模板 (新增技术成熟度评估模块)
06-conclusion-template.md        # 结论模板 (支持独立/合并两种方式)
07-multi-technology-integration-template.md  # 多技术集成展示模板 (新增)
```

### **结果部分子模板** (6个)
```
04.1-algorithm-signal-processing-results.md      # 算法与信号处理结果
04.2-multi-scenario-clinical-applications-results.md  # 多场景临床应用结果 (新增日常使用场景验证)
04.3-extended-data-supplementary-results.md      # 扩展数据与补充结果
04.4-mechanical-characterization-results.md      # 机械特性测试结果
04.5-results-discussion-combined-results.md      # 结果讨论合并展示
04.6-technology-comparison-results.md            # 技术对比分析结果
```

### **专门应用模板** (1个)
```
review-response-letter-template.md  # 审稿回复信模板
```

## 🎯 快速选择指南

### **按研究类型选择**

#### **临床医学研究** → Nature Medicine, NEJM
```
主模板: 01 + 02 + 03 + 04 + 05 + 06
子模板: 04.2 + 04.3
重点: 临床验证 + 详细数据
```

#### **工程技术创新** → Nature, Science
```
主模板: 01 + 02 + 03 + 04 + 05 + 06
子模板: 04.1 + 04.4 + 04.6
重点: 算法创新 + 性能测试 + 技术对比
```

#### **产品开发研究** → Applied Sciences
```
主模板: 01 + 02 + 03 + 04 + 05 + 06
子模板: 04.5 + 04.6
重点: 综合展示 + 市场分析
```

#### **基础技术研究** → IEEE Transactions
```
主模板: 01 + 02 + 03 + 04 + 05 + 06
子模板: 04.1 + 04.3
重点: 算法创新 + 技术细节
```

## 📋 使用流程

### **步骤1: 确定研究导向**
- **医学导向**: 选择医学导向选项 (临床价值、患者获益)
- **技术导向**: 选择技术导向选项 (技术创新、商业价值)

### **步骤2: 选择结构方式**
- **传统分离式**: 结果→讨论→结论 (适合传统期刊)
- **现代合并式**: 结果讨论合并 (适合现代期刊)

### **步骤3: 配置子模板**
- 根据研究特点选择相应的04.X子模板
- 可单独使用或组合使用多个子模板

### **步骤4: 质量检查**
- 使用各模板的质量检查清单
- 确保内容完整性和逻辑连贯性

## 🔗 模板特色功能

### **双导向支持**
- **医学导向**: 死亡率统计、临床需求、患者获益
- **技术导向**: 技术分类、市场需求、商业价值

### **灵活结构选择**
- **分离式结构**: 传统的章节分离组织
- **合并式结构**: 现代的结果讨论合并

### **专业化子模板**
- **算法处理**: 详细的算法开发和验证指导
- **临床应用**: 多场景临床验证展示框架
- **机械测试**: 全面的机械性能验证
- **技术对比**: 系统性竞争分析框架

## 📊 模板适用性矩阵

| 研究类型 | 主要期刊 | 推荐模板组合 | 写作重点 |
|---------|---------|-------------|---------|
| 临床医学 | Nature Medicine | 04.2 + 04.3 | 临床验证 + 详细数据 |
| 工程技术 | Nature/Science | 04.1 + 04.4 + 04.6 | 算法 + 性能 + 对比 |
| 产品开发 | Applied Sciences | 04.5 + 04.6 | 应用 + 市场 |
| 基础研究 | IEEE Trans | 04.1 + 04.3 | 算法 + 技术细节 |
| 多学科交叉 | Science Advances | 04.1 + 04.2 + 04.4 | 综合验证 |

## 💡 使用建议

### **新手建议**
1. 从单一子模板开始熟悉结构
2. 参考模板中的具体示例
3. 使用质量检查清单确保完整性

### **进阶使用**
1. 根据期刊要求灵活组合模板
2. 根据审稿意见调整模板选择
3. 自定义模板内容以适应特定需求

### **质量保证**
1. 每个模板都包含详细的使用说明
2. 提供基于真实论文的示例
3. 包含全面的质量检查清单

---

## 📝 更新日志

**v2.1** (当前版本)
- 基于柔性可穿戴听诊器论文进一步完善模板系统
- 新增技术演进逻辑链和地域差异化需求分析模块
- 增加多技术集成展示专门模板
- 强化日常使用场景验证和技术成熟度评估
- 提升模板对复杂系统和跨学科研究的支持能力

**v2.0** (前一版本)
- 基于两篇高质量样本论文完善模板系统
- 重组结果模板为主模板+6个子模板结构
- 增加医学/技术双导向支持
- 新增机械测试、技术对比等专门模板

**v1.0** (初始版本)
- 基于Nature Medicine论文创建基础模板系统
- 包含6个核心写作模板和3个专门模板

---

通过这套完整的模板系统，研究者可以高效地撰写出符合不同期刊要求的高质量学术论文。
